/**
 * @fileoverview Service for validating form fields
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { differenceInYears, parse, isValid } from 'date-fns';
import { DATE_FORMAT } from 'lib-ui-kit';


@Injectable()
export class FormValidatorService {
    /**
     * Service for validating form fields.
     *
     * This service provides methods for creating validator functions for various form fields,
     * Validators functions:
     *  - passwordMatchValidator : Returns a validator function that checks if password and confirm password match
     */
    /**
     * Factory function to create a password match validator.
     *
     * @param {string} passwordField - The name of the password field
     * @param {string} confirmPasswordField - The name of the confirm password field
     * @returns {ValidatorFn} A validator function that checks if password and confirm password match
     */
    passwordMatchValidator(
        passwordField: string,
        confirmPasswordField: string
    ): ValidatorFn {
        return (form: AbstractControl): ValidationErrors | null => {
            const password = form.get(passwordField)?.value;
            const confirmPassword = form.get(confirmPasswordField)?.value;

            if (password !== confirmPassword) {
                form.get(confirmPasswordField)?.setErrors({
                    passwordMismatch: true,
                });
                return { passwordMismatch: true };
            }

            return null;
        };
    }

    /**
     * Factory function to create a date of birth validator.
     *
     * @param {number} minAge - The minimum age required
     * @param {number} [maxAge] - The maximum age required (optional)
     * @returns {ValidatorFn} A validator function that checks if the date of birth is valid and within the age range
     */
    dobValidatorFactory(minAge: number, maxAge?: number): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const dateOfBirth = control.value;
            if (!dateOfBirth) return null;

            const parsedDate = parse(dateOfBirth, DATE_FORMAT, new Date());
            if (!isValid(parsedDate)) {
                return { invalidDateOfBirth: true };
            }

            const age = differenceInYears(new Date(), parsedDate);

            if (minAge !== undefined && age < minAge) {
                return { minAge: { requiredAge: minAge, actualAge: age } };
            }
            if (maxAge !== undefined && age >= maxAge) {
                return { maxAge: { requiredAge: maxAge, actualAge: age } };
            }

            return null;
        };
    }
    
}
