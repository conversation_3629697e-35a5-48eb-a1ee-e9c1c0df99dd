/**
 * @fileoverview
 * A phone input component that allows the user to input a phone number.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-22
 */
import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    forwardRef,
    inject,
    Injector,
    input,
    OnInit,
    signal,
    TemplateRef
} from '@angular/core';
import {
    ControlValueAccessor,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { ClickOutsideDirective } from '../../../directives/click-outside.directive';
import { isTemplateRef } from '../../../utils';
import { Country } from 'lib-ui-kit';
import { waiverConstants } from 'projects/online-waiver/src/app/constants/waiver.constant';

@Component({
    selector: 'lib-phone-input',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, ClickOutsideDirective],
    templateUrl: './phone-input.component.html',
    styleUrl: './phone-input.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => PhoneInputComponent),
            multi: true,
        },
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PhoneInputComponent implements ControlValueAccessor, OnInit {
    id = input<string>('');
    label = input<string>('');
    name = input<string>('');
    placeholder = input<string>('');
    countries = input.required<Country[]>();
    defaultCountry = input<Country>();
    required = input<boolean>(false);
    isCountryCodeRequired = input<boolean>(true);
    errorMessages = input<Record<string, string | TemplateRef<Component> | undefined>>();
    isDisabled = false;
    selectedCountry: Country = this.defaultCountry() ?? { name: '', dialCode: '' };
    isDropdownOpen = false;
    value = signal<string | null>(null);
    isTemplateRef = isTemplateRef;

    private onChange: (value: string | null) => void = () => { };
    private onTouched: () => void = () => { };
    ngControl: NgControl | null = null;
    private injector = inject(Injector);

    ngOnInit(): void {
        const ngControl = this.injector.get(NgControl, null);
        if (ngControl) {
            this.ngControl = ngControl;
        }

        if (this.countries()?.length > 0) {
            this.selectedCountry = this.countries()?.[0];
            this.validatePhoneNumber(this.value());
        }
    }

    /**
     * Checks if the phone input is required.
     */
    get isRequired(): boolean {
        return (
            this.required() ||
            !!this.ngControl?.control?.hasValidator?.(Validators.required)
        );
    }

    /**
     * Writes the value to the phone input.
     */
    writeValue(value: string | null): void {
        this.value.set(value);
    }

    /**
     * Registers a change handler for the phone input.
     */
    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    /**
     * Registers a touched handler for the phone input.
     */
    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    /**
     * Sets the disabled state of the phone input.
     */
    setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    /**
     * Handles the input change event for the phone input.
     */
    onInputChange(event: Event): void {
        const input = event.target as HTMLInputElement;
        const value = input.value;
        this.value.set(value);
        this.onChange(value);
        this.validatePhoneNumber(value);
    }

    /**
     * Handles the blur event for the phone input.
     */
    onBlur(): void {
        this.onTouched();
        this.validatePhoneNumber(this.value());
    }

    /**
     * Toggles the dropdown for the phone input.
     */
    toggleDropdown(): void {
        this.isDropdownOpen = !this.isDropdownOpen;
    }

    /**
     * Selects a country for the phone input.   
     */
    selectCountry(country: Country): void {
        this.selectedCountry = country;
        this.isDropdownOpen = false;
        this.validatePhoneNumber(this.value());
    }

    /**
     * Checks if the phone input has an error.
     */
    hasError(): boolean {
        return (
            !!this.ngControl?.control?.invalid &&
            !!this.ngControl?.control?.touched
        );
    }

    /**
     * Gets the error message for the phone input.
     */
    get errorMessage() {
        const errors = this.ngControl?.control?.errors;
        if (!errors) return null;

        const errorKeys = Object.keys(errors);
        if (errorKeys.length > 0) {
            const firstErrorKey = errorKeys[0];
            return this.errorMessages()?.[firstErrorKey] ?? null;
        }
        return null;
    }

    /**
     * Gets the format examples for the phone input.
     */
    getFormatExamples(): string {
        return this.selectedCountry.examples?.join(', ') ?? '';
    }

    /**
     * Handles the key down event for the phone input.
     */
    onKeyDown(event: KeyboardEvent): void {
        const allowedCharacters = this.selectedCountry?.allowedCharacters;
        
        // Check if the character is allowed
        if (allowedCharacters && !allowedCharacters.test(event.key)) {
            event.preventDefault();
        }
    }

    /**
     * Validates the phone number.
     */
    private validatePhoneNumber(value: string | null): void {
        if (!value) {
            this.clearErrors();
            return;
        }

        const pattern = this.selectedCountry?.numericPattern ?? waiverConstants.NUMERIC_PATTERN_REGEX;
        // Only validate if a pattern is provided
        if (pattern) {
            if (!pattern.test(value.trim())) {
                this.setOuterErrors({ pattern: true });
            } else {
                this.clearErrors();
            }
        } else {
            // If no pattern is provided, clear any existing pattern errors
            this.clearErrors();
        }
    }

    /**
     * Sets the outer errors for the phone input other than pattern error. 
     */
    private setOuterErrors(errors: any): void {
        const existingErrors = this.ngControl?.control?.errors ?? {};
        this.ngControl?.control?.setErrors({
            ...existingErrors,
            ...errors,
        });
    }

    /**
     * Clears the errors for the phone input.
     */
    private clearErrors(): void {
        const existingErrors = this.ngControl?.control?.errors ?? {};
        const { pattern, ...otherErrors } = existingErrors;

        const hasOtherErrors = Object.keys(otherErrors).length > 0;

        this.ngControl?.control?.setErrors(hasOtherErrors ? otherErrors : null);
    }
}
