<div libClickOutside (clickOutside)="isDropdownOpen = false" (click)="$event.stopPropagation()" tabindex="0"
    (keydown.escape)="isDropdownOpen = false">
    @if (label()) {
    <label class="block text-xs text-primary mb-2" [for]="id()">
        {{ label() }}
        @if (isRequired) {
        <span class="text-red-500">*</span>
        }
    </label>
    }

    <div class="flex items-center text-sm bg-surface-white border border-surface rounded-xl focus-within:outline-none focus-within:ring-1 focus-within:ring-secondary-blue"
        [ngClass]="{
      'border-none ring-1 ring-feedback-error': hasError(),
    }">
        @if (isCountryCodeRequired()) {
        <div class="relative my-3.5 border-r border-surface px-3.5">
            <button [disabled]="isDisabled" type="button" (click)="toggleDropdown()"
                class="w-full flex items-center space-x-2">
                @if(selectedCountry.countryFlagIcon){
                    <img [src]="selectedCountry.countryFlagIcon" [alt]="selectedCountry.dialCode" class="h-4 w-6 mr-1" />
                }
                <span class="text-primary">{{ selectedCountry.dialCode }}</span>
                <img src="/assets/icons/chevron-down.svg" alt="chevron-down" />
            </button>

            @if (isDropdownOpen) {
            <div class="absolute z-30 w-max mt-4 -ml-3 bg-surface-white rounded-3xl shadow-lg border border-surface">
                <div class="py-1">
                    @for (country of countries(); track $index) {
                    <button [disabled]="isDisabled" type="button" (click)="selectCountry(country)"
                        class="block w-full text-left px-4 py-2 text-sm text-primary hover:bg-surface-lightest"
                        [class.font-medium]="
                            country.dialCode === selectedCountry.dialCode
                        ">
                        <div class="flex items-center">
                            @if(country.countryFlagIcon){
                            <img [src]="country.countryFlagIcon" [alt]="country.dialCode" class="h-4 w-6 mr-2" />
                            }
                            <span>{{ country.name }} ({{
                                country.dialCode
                                }})</span>
                        </div>
                    </button>
                    }
                    @empty {
                    <div class="text-center text-sm text-primary py-1 px-4">No countries found</div>
                    }
                </div>
            </div>
            }
        </div>
        }
        <input type="tel" [id]="id()" [name]="name()" [value]="value()" [placeholder]="placeholder()"
            [disabled]="isDisabled" (blur)="onBlur()" (input)="onInputChange($event)" (keydown)="onKeyDown($event)"
            class="w-full flex-1 py-3.5 px-4 rounded-xl overflow-hidden placeholder:text-neutral-dark" />
    </div>

    @if (hasError()) {
    <div class="text-feedback-error text-xs mt-2">
        @if (isTemplateRef(errorMessage)) {
        <ng-container *ngTemplateOutlet="$any(errorMessage)"></ng-container>
        } @else if (ngControl?.control?.errors?.['pattern']) { Please enter a
        valid phone number. @if(getFormatExamples()){
            Examples: {{ getFormatExamples() }}
        }
        } @else {
        {{ errorMessage }}
        }
    </div>
    }
</div>