/**
 * @fileoverview
 * A select component that allows the user to select an option from a list.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-22
 */

import { CommonModule } from '@angular/common';
import {
    Component,
    forwardRef,
    inject,
    Injector,
    input,
    OnInit,
    signal,
    TemplateRef,
    ChangeDetectionStrategy
} from '@angular/core';
import {
    ControlValueAccessor,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { ClickOutsideDirective } from '../../../directives/click-outside.directive';
import { DropdownOption } from '../../../interfaces/form-control-interfaces';
import { isTemplateRef } from '../../../utils';

@Component({
    selector: 'lib-select',
    imports: [CommonModule, ReactiveFormsModule, ClickOutsideDirective],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SelectComponent),
            multi: true,
        },
    ],
    templateUrl: './select.component.html',
    styleUrl: './select.component.css',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectComponent implements ControlValueAccessor, OnInit {
    id = input<string>('');
    label = input<string>('');
    name = input<string>('');
    options = input<DropdownOption[]>([]);
    placeholder = input<string>('Choose an option');
    required = input<boolean>(false);
    errorMessages = input<Record<string, string | TemplateRef<Component> | undefined>>();
    position = input<'top' | 'bottom'>('bottom');
    isTemplateRef = isTemplateRef;
    isDisabled = false;
    ngControl: NgControl | null = null;
    readonly dropdownOpen = signal<boolean>(false);
    readonly selectedOption = signal<DropdownOption | null>(null);
    private injector = inject(Injector);
    private onChange: (value: string | null) => void = () => { };
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        const ngControl = this.injector.get(NgControl, null);
        if (ngControl) {
            this.ngControl = ngControl;
        }
    }

    /**
     * Checks if the select is required.
     */
    get isRequired(): boolean {
        return (
            this.required() ||
            !!this.ngControl?.control?.hasValidator?.(Validators.required)
        );
    }

    /**
     * Toggles the dropdown.
     */
    toggleDropdown() {
        if (this.isDisabled) return;
        this.dropdownOpen.set(!this.dropdownOpen());
    }

    /**
     * Selects an option.   
     */
    selectOption(option: DropdownOption) {
        if (this.isDisabled) return;
        this.selectedOption.set(option);
        this.onChange(option.value);
        this.dropdownOpen.set(false);
    }

    /**
     * Handles the keydown event.
     */
    onKeydown(event: KeyboardEvent) {
        if (this.isDisabled) return;
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.toggleDropdown();
        }
    }

    /**
     * Writes the value.
     */
    writeValue(value: string | null): void {
        const selected =
            this.options().find((opt) => opt.value === value) || null;
        this.selectedOption.set(selected);
    }

    /**
     * Registers the change handler.
     */
    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    /**
     * Registers the touched handler.
     */
    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    /**
     * Handles the blur event.
     */
    onBlur(): void {
        this.onTouched();
    }

    /**
     * Sets the disabled state.
     */
    setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    /**
     * Checks if there is an error.
     */
    hasError(): boolean {
        return (
            !!this.ngControl?.control?.invalid &&
            !!this.ngControl?.control?.touched
        );
    }

    /**
     * Gets the error message.
     */
    get errorMessage() {
        const errors = this.ngControl?.control?.errors;
        if (!errors) return null;

        const errorKeys = Object.keys(errors);
        if (errorKeys.length > 0) {
            const firstErrorKey = errorKeys[0];
            return this.errorMessages()?.[firstErrorKey] ?? null;
        }
        return null;
    }

    get customClasses() {
        let className = '';

        if (this.hasError()) {
            className = 'border-none ring-1 ring-feedback-error';
        }

        else if (this.dropdownOpen()) {
            className = 'ring-1 ring-secondary-blue';
        }

        else if(this.isDisabled) {
            className = 'cursor-not-allowed';
        }
        return className;
    }


}
