<div>
    @if (label()) {
    <label class="block text-xs font-medium text-primary mb-2" [for]="id()">
        {{ label() }}
    </label>
    }

    <div class="relative w-full">
        @if (previewUrl()) {
        <!-- Preview Mode -->
        <div class="relative w-full">
            <div class="flex items-center w-full py-3.5 px-4 bg-surface-white rounded-xl border border-surface">
                <div class="rounded overflow-hidden mr-3 flex-shrink-0">
                    <img [src]="previewUrl()" alt="Profile picture preview"
                        class="w-5 h-5 object-cover object-center" />
                </div>
                <span class="text-sm text-neutral-dark flex-grow truncate">
                    {{ getFile()?.name || 'image.jpg' }}
                </span>
                <button type="button" (click)="removePicture()" (keyup.enter)="removePicture()" class="flex-shrink-0"
                    aria-label="Remove profile picture">
                    <img src="assets/icons/close-red.svg" class="w-5 h-5" alt="Remove profile picture">
                </button>
            </div>
        </div>
        } @else {
        <!-- Upload Mode -->
        <div class="w-full">
            <div class="relative w-full py-3.5 px-4 bg-surface-white rounded-xl border border-surface flex items-center transition-all focus-within:ring-1 focus-within:ring-secondary-blue"
                (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onDrop($event)" tabindex="0"
                role="button" aria-label="Upload profile picture" [attr.aria-busy]="isLoading()"
                [attr.aria-invalid]="hasError()" [class.ring-1]="hasError()" [class.ring-feedback-error]="hasError()">
                <div class="flex items-center w-full">
                    @if (isLoading()) {
                    <div role="status" class="flex items-center w-full">
                        <div class="w-5 h-5 border-2 border-t-transparent rounded-full animate-spin mr-3">
                        </div>
                        <span class="text-sm text-neutral-dark">Processing...</span>
                        <span class="sr-only">Uploading profile picture</span>
                    </div>
                    } @else {
                    <img src="assets/icons/user.svg" class="w-5 h-5 mr-3 flex-shrink-0" alt="Upload profile picture">
                    <span class="text-sm text-neutral-dark">
                        {{ placeholder() }}
                    </span>
                    }
                </div>

                <input type="file" [name]="name()" [id]="id()" [accept]="accept()" (change)="onFileSelected($event)"
                    (blur)="onBlur()" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    [disabled]="disabled() || isLoading()" />
            </div>
        </div>
        }
    </div>

    <!-- Error & Description Handling -->
    @if (hasError()) {
    <div class="text-feedback-error text-xs mt-2">
        @if (isTemplateRef(errorMessage)) {
        <ng-container [ngTemplateOutlet]="$any(errorMessage)"></ng-container>
        } @else {
        {{ errorMessage }}
        }
    </div>
    } @else if (description()) {
    <div class="text-neutral-dark text-xs mt-2">
        {{ description() }}
    </div>
    }
</div>