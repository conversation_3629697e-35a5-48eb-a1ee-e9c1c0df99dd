/**
 * @fileoverview
 * Picture Uploader Component
 * 
 * A reusable Angular component that provides drag-and-drop and click-to-upload
 * functionality for image files. Implements ControlValueAccessor for seamless
 * integration with Angular reactive forms.
 * 
 * Features:
 * - Drag and drop file upload
 * - Click to browse file upload
 * - File size validation
 * - File type validation
 * - Preview generation
 * - Base64 conversion
 * - Error handling and display
 * - Customizable styling and messaging
 * 
 * @example
 * ```html
 * <lib-picture-uploader
 *   [label]="'Profile Picture'"
 *   [required]="true"
 *   [maxSize]="5242880"
 *   [errorMessages]="{ required: 'Profile picture is required' }"
 *   formControlName="profilePicture">
 * </lib-picture-uploader>
 * ```
 *
 * <AUTHOR> Bhat
 * @version 1.0.0
 * @createdAt 2025-08-05
 */
import { CommonModule } from '@angular/common';
import {
    Component,
    forwardRef,
    inject,
    Injector,
    input,
    signal,
    TemplateRef,
} from '@angular/core';
import {
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    ControlValueAccessor
} from '@angular/forms';
import { appCoreConstant } from 'lib-app-core';
import { isTemplateRef } from '../../../utils';

/**
 * Interface representing picture data with file, preview URL, and base64 string
 */
export interface PictureData {
    /** The uploaded file object */
    file: File;
    /** Data URL for preview display */
    previewUrl: string;
    /** Base64 encoded string of the file (without data URL prefix) */
    base64: string;
}


@Component({
    selector: 'lib-picture-uploader',
    imports: [ReactiveFormsModule, CommonModule],
    templateUrl: './picture-uploader.component.html',
    styleUrl: './picture-uploader.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => PictureUploaderComponent),
            multi: true,
        },
    ],
})
export class PictureUploaderComponent implements ControlValueAccessor {
    id = input<string>('');
    label = input<string>('Profile Picture');
    name = input<string>('profile-picture');
    placeholder = input<string>('Browse & upload image');
    required = input<boolean>(false);
    disabled = input<boolean>(false);
    errorMessages = input<Record<string, string | TemplateRef<Component> | undefined>>();
    customClass = input<string | null>(null);
    description = input<string | null>(null);
    accept = input<string>(appCoreConstant.ACCEPTED_IMAGE_FILE_TYPES);
    maxSize = input<number>(appCoreConstant.MAX_IMAGE_FILE_SIZE);
    readonly previewUrl = signal<string | null>(null);
    readonly isDragOver = signal<boolean>(false);
    readonly isLoading = signal<boolean>(false);
    readonly isDisabled = signal<boolean>(false);
    readonly fileTypeError = signal<boolean>(false);
    readonly fileSizeError = signal<boolean>(false);
    isTemplateRef = isTemplateRef;

    /** Reference to the NgControl for form integration */
    private ngControl: NgControl | null = null;
    
    /** Injector instance for dependency injection */
    private injector = inject(Injector);

    /** Callback function for value changes (ControlValueAccessor) */
    private onChange: (value: PictureData | null) => void = () => { };
    
    /** Callback function for touch events (ControlValueAccessor) */
    private onTouched: () => void = () => { };

    /**
     * Lifecycle hook that is called after data-bound properties are initialized.
     * Sets up the NgControl reference for form integration.
     */
    ngOnInit(): void {
        const ngControl = this.injector.get(NgControl, null);
        if (ngControl) {
            this.ngControl = ngControl;
        }
    }   

    /**
     * Writes a value to the component (ControlValueAccessor implementation).
     * Updates the preview URL and clears any existing errors.
     * 
     * @param value - The PictureData object or null to clear the component
     */
    writeValue(value: PictureData | null): void {
        this.previewUrl.set(value?.previewUrl ?? null);
        if (!value) {
            this.fileTypeError.set(false);
            this.fileSizeError.set(false);
            this.ngControl?.control?.setErrors(null);
        }
    }

    /**
     * Registers a callback function that is called when the value changes
     * (ControlValueAccessor implementation).
     * 
     * @param fn - The callback function to register
     */
    registerOnChange(fn: (value: PictureData | null) => void): void {
        this.onChange = fn;
    }

    /**
     * Registers a callback function that is called when the component is touched
     * (ControlValueAccessor implementation).
     * 
     * @param fn - The callback function to register
     */
    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    /**
     * Sets the disabled state of the component (ControlValueAccessor implementation).
     * 
     * @param isDisabled - Whether the component should be disabled
     */
    setDisabledState(isDisabled: boolean): void {
        this.isDisabled.set(isDisabled);
    }

    /**
     * Handles blur events on the component.
     * Triggers the onTouched callback for form validation.
     */
    onBlur(): void {
        this.onTouched();
    }

    /**
     * Handles file selection from the file input element.
     * Processes the selected file and validates it.
     * 
     * @param event - The file input change event
     */
    async onFileSelected(event: Event): Promise<void> {
        const input = event.target as HTMLInputElement;
        const file = input.files?.[0];
        if (file) await this.handleFile(file);
    }

    /**
     * Handles drag over events on the component.
     * Prevents default behavior and sets the drag over state.
     * 
     * @param event - The drag over event
     */
    onDragOver(event: DragEvent): void {
        event.preventDefault();
        this.isDragOver.set(true);
    }

    /**
     * Handles drag leave events on the component.
     * Prevents default behavior and clears the drag over state.
     * 
     * @param event - The drag leave event
     */
    onDragLeave(event: DragEvent): void {
        event.preventDefault();
        this.isDragOver.set(false);
    }

    /**
     * Handles drop events on the component.
     * Processes the dropped file and validates it.
     * 
     * @param event - The drop event
     */
    async onDrop(event: DragEvent): Promise<void> {
        event.preventDefault();
        this.isDragOver.set(false);
        const file = event.dataTransfer?.files[0];
        if (file) await this.handleFile(file);
    }

    /**
     * Handles file processing including validation, preview generation, and form updates.
     * Validates file size and type, generates preview and base64 data, and updates
     * the form control value.
     * 
     * @param file - The file to process
     */
    private async handleFile(file: File): Promise<void> {
        this.resetErrors();

        // Validate size
        if (file.size > this.maxSize()) {
            this.fileSizeError.set(true);
            this.setError(
                `File size must be less than ${this.formatFileSize(this.maxSize())}.`
            );
            return;
        }

        // Generate preview and base64
        this.isLoading.set(true);
        try {
            const { previewUrl, base64 } = await this.convertFileToBase64(file);
            const profilePictureData: PictureData = { file, previewUrl, base64 };

            this.previewUrl.set(previewUrl);
            this.onChange(profilePictureData);
            this.onTouched();
        } catch {
            this.setError('Failed to process image. Please try again.');
        } finally {
            this.isLoading.set(false);
        }
    }

    /**
     * Converts a file to base64 format and generates a preview URL.
     * Returns both the full data URL (for preview) and the base64 string
     * without the data URL prefix (for API calls).
     * 
     * @param file - The file to convert
     * @returns Promise containing preview URL and base64 string
     */
    private convertFileToBase64(file: File): Promise<{ previewUrl: string; base64: string }> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target?.result as string;
                // Extract base64 string (remove data URL prefix)
                const base64 = result.includes(',') ? result.split(',')[1] : '';
                resolve({ previewUrl: result, base64 });
            };
            reader.onerror = () => reject();
            reader.readAsDataURL(file);
        });
    }

    /**
     * Gets the base64 string (without data URL prefix) from the current value.
     * Useful for API calls that require just the base64 data.
     * 
     * @returns The base64 string or null if no file is selected
     */
    getBase64String(): string | null {
        const value = this.ngControl?.control?.value;
        return value?.base64 ?? null;
    }

    /**
     * Gets the complete data URL (with data URL prefix) from the current value.
     * Useful for displaying images or storing complete data URLs.
     * 
     * @returns The complete data URL or null if no file is selected
     */
    getBase64DataUrl(): string | null {
        const value = this.ngControl?.control?.value;
        return value?.previewUrl ?? null;
    }

    /**
     * Gets the File object from the current value.
     * Useful for form submission or additional file processing.
     * 
     * @returns The File object or null if no file is selected
     */
    getFile(): File | null {
        const value = this.ngControl?.control?.value;
        return value?.file ?? null;
    }

    /**
     * Removes the currently selected picture from the component.
     * Clears the preview, resets the form value, and clears any errors.
     */
    removePicture(): void {
        this.previewUrl.set(null);
        this.onChange(null);
        this.onTouched();
        this.resetErrors();
    }

    /**
     * Sets a custom error message on the form control.
     * Used for displaying validation errors to the user.
     * 
     * @param message - The error message to display
     */
    private setError(message: string): void {
        this.ngControl?.control?.setErrors({ customError: message });
    }

    /**
     * Checks if the component has any validation errors.
     * Returns true if the control is invalid and has been touched.
     * 
     * @returns True if there are validation errors, false otherwise
     */
    hasError(): boolean {
        return !!this.ngControl?.control?.invalid && !!this.ngControl?.control?.touched;
    }

    /**
     * Gets the current error message to display.
     * Prioritizes custom errors over standard validation errors.
     * 
     * @returns The error message string or null if no errors
     */
    get errorMessage() {
        const errors = this.ngControl?.control?.errors;
        if (!errors) return null;

        if (errors['customError']) {
            return errors['customError'] as string;
        }

        const errorKeys = Object.keys(errors);
        if (errorKeys.length > 0) {
            const firstErrorKey = errorKeys[0];
            return this.errorMessages()?.[firstErrorKey] ?? null;
        }
        return null;
    }

    /**
     * Generates dynamic CSS classes based on component state.
     * Applies error styling, description styling, and custom classes.
     * 
     * @returns Space-separated string of CSS classes
     */
    dynamicClass(): string {
        return [
            this.hasError() && 'ring-1 ring-feedback-error',
            this.description() && 'ring-1 ring-surface',
            this.customClass()
        ].filter(Boolean).join(' ');
    }

    /**
     * Formats a file size in bytes to a human-readable string.
     * Converts bytes to KB, MB, or GB as appropriate.
     * 
     * @param bytes - The file size in bytes
     * @returns Formatted file size string (e.g., "2.5 MB")
     */
    private formatFileSize(bytes: number): string {
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Gets the maximum file size in MB for display purposes.
     * Converts the maxSize from bytes to megabytes.
     * 
     * @returns Maximum file size in MB
     */
    get maxFileSize(): number {
        return parseFloat((this.maxSize() / 1024 / 1024).toFixed(2));
    }

    resetErrors(): void {
        this.fileTypeError.set(false);
        this.fileSizeError.set(false);
        this.ngControl?.control?.setErrors(null);
    }
}
