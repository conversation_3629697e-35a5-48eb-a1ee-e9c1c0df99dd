@if (isOpen()) {
<div #popupMask
    class="fixed h-full w-full inset-0 flex items-center justify-center bg-modal-overlay transition-colors duration-200 ease-in-out z-50"
    (keydown)="handleKeydown($event)" tabindex="-1">
    <dialog #modalContainer class="bg-surface-white rounded-4xl mx-6 relative top-0 animate-fadeIn" libClickOutside
        (clickOutside)="close()" role="dialog" aria-modal="true" [class.closing]="isClosing()"
        [class.animate-fadeOut]="isClosing()" open>
        @if (dialogueHeader()) {
        <header class="flex items-center justify-between px-5 md:px-6 pt-5 md:pt-6">
            <h2 class="text-lg font-bold text-primary">{{ dialogueHeader() }}</h2>
            <button type="button" (click)="close()" aria-label="Close" tabindex="0" class="cursor-pointer">
                <img src="assets/icons/close-red.svg" alt="Close" class="w-6 h-6" />
            </button>
        </header>
        } @else {
        <header class="relative">
            <button type="button" class="absolute top-5 right-5 cursor-pointer" (click)="close()" aria-label="Close" tabindex="0">
                <img src="assets/icons/close-red.svg" alt="Close" class="w-6 h-6" />
            </button>
        </header>
        }
        <div class="overflow-y-visible max-h-[90vh]" [ngClass]="{ 'pt-5 md:pt-6': dialogueHeader() }">
            <ng-container *ngTemplateOutlet="modalContent();  context: modalContext"></ng-container>
        </div>
    </dialog>
</div>
}