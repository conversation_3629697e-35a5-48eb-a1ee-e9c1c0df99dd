/**
 * @fileoverview Skeleton loader component for loading skeletons in the UI
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
*/
import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-skeleton-loader',
  imports: [CommonModule],
  templateUrl: './skeleton-loader.component.html',
  styleUrl: './skeleton-loader.component.css'
})
export class SkeletonLoaderComponent {
  count = input<number>(1);
  wrapperClass = input<string>('');
  skeletonClass = input<string>('');

  createArray(n: number): undefined[] {
    return Array(n);
  }
}
