/**
 * @fileoverview Dynamic form field interface
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
 */

import { ValidatorFn } from '@angular/forms';
import { DropdownOption } from 'lib-ui-kit';

export interface DynamicFormField {
    id: string;
    fieldName: string;
    label: string;
    type:
        | 'text'
        | 'email'
        | 'number'
        | 'phone'
        | 'password'
        | 'date'
        | 'select'
        | 'checkbox';

    required: boolean;
    options?: DropdownOption[];
    placeholder?: string;
    min?: number | string;
    max?: number | string;
    disabled?: boolean;
    validators?: {
        validatorFn?: ValidatorFn | ValidatorFn[];
        pattern?: string;
        minLength?: number;
        maxLength?: number;
    };
    countryCodeOptions?: Country[];  
}

export interface Country {
    name: string;
    dialCode: string;
    countryFlagIcon?: string;
    examples?: string[];
    allowedCharacters?: RegExp;
    numericPattern?: RegExp;
  }
  