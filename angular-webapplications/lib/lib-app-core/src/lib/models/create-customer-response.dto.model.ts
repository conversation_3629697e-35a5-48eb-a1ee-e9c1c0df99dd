/**
 * @fileoverview Create customer response DTO model
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
 */
import { BaseDTO } from './base-dto.model';

export class CreateCustomerResponseDTOModel extends BaseDTO<CreateCustomerResponseDTOModel> {
    Id: number = -1;
    IsActive: boolean = true;
    IsBonusLoaded: boolean = false;
    ProfileId: number = -1;
    MembershipId: number = -1;
    Title: string = '';
    FirstName: string = '';
    NickName: string | null = null;
    MiddleName: string = '';
    LastName: string = '';
    ExternalSystemReference: string = '';
    CustomerType: number = 0;
    UniqueIdentifier: string = '';
    TaxCode: string = '';
    DateOfBirth: string | null = null;
    Gender: string | null = null;
    Anniversary: string | null = null;
    TeamUser: boolean = false;
    RightHanded: boolean = false;
    OptInPromotions: boolean = false;
    OptInPromotionsMode: string | null = null;
    PolicyTermsAccepted: boolean = false;
    Company: string = '';
    UserName: string = '';
    PhotoURL: string | null = null;
    IdProofFileURL: string | null = null;
    LastLoginTime: string | null = null;
    Designation: string = '';
    CustomDataSetId: number = -1;
    Notes: string = '';
    CardNumber: string | null = null;
    Channel: string | null = null;
    Verified: boolean = false;

    AddressDTOList: AddressDTOModel[] = [];
    ContactDTOList: ContactDTOModel[] = [];
    ProfileDTO: ProfileDTOModel = new ProfileDTOModel();
    CustomerVerificationDTO: any = null;
    CustomDataSetDTO: CustomDataSetDTOModel = new CustomDataSetDTOModel();
    PhoneNumber: string = '';
    PhoneContactDTO: any = null;
    Password: string | null = null;
    LatestAddressDTO: AddressDTOModel = new AddressDTOModel();
    SecondaryPhoneNumber: string = '';
    FBUserId: string = '';
    FBAccessToken: string = '';
    TWAccessToken: string = '';
    TWAccessSecret: string = '';
    Email: string = '';
    WeChatAccessToken: string = '';
    IsChanged: boolean = false;
    IsChangedRecursive: boolean = true;

    CustomerCuponsDT: any = null;
    AccountDTOList: any[] = [];
    CustomerMembershipProgressionDTOList: any[] = [];
    CustomerMembershipRewardsLogDTOList: any[] = [];
    CustomerSignedWaiverDTOList: any[] = [];
    CustomerApprovalLogDTOList: any[] = [];
    ActiveCampaignCustomerInfoDTOList: any[] = [];

    LastVisitedDate: string = '0001-01-01T00:00:00';
    Status: string = '';
    StatusId: number = -1;
    StatusChangeDate: string = '';

    constructor() {
        super();
    }
}

export class AddressDTOModel {
    Id: number = -1;
    ProfileId: number = -1;
    AddressTypeId: number = -1;
    AddressType: number = 0;
    Line1: string = '';
    Line2: string = '';
    Line3: string = '';
    City: string = '';
    StateId: number = -1;
    CountryId: number = -1;
    PostalCode: string = '';
    StateCode: string | null = null;
    StateName: string | null = null;
    CountryName: string | null = null;
    IsActive: boolean = true;
    CreatedBy: string = '';
    CreationDate: string = '';
    LastUpdatedBy: string = '';
    LastUpdateDate: string = '';
    SiteId: number = -1;
    MasterEntityId: number = -1;
    SynchStatus: boolean = false;
    Guid: string = '';
    IsDefault: boolean = false;
    ContactDTOList: ContactDTOModel[] = [];
    IsChanged: boolean = false;
    IsChangedRecursive: boolean = false;

}

export class ContactDTOModel {
}

export class ProfileDTOModel {
    Id: number = -1;
    ProfileTypeId: number = -1;
    ProfileType: number = 1;
    Title: string = '';
    FirstName: string = '';
    MiddleName: string = '';
    LastName: string = '';
    NickName: string | null = null;
    Notes: string = '';
    DateOfBirth: string | null = null;
    Gender: string | null = null;
    Anniversary: string | null = null;
    PhotoURL: string | null = null;
    RightHanded: boolean = false;
    TeamUser: boolean = false;
    UniqueIdentifier: string = '';
    IdProofFileURL: string | null = null;
    TaxCode: string = '';
    Designation: string = '';
    Company: string = '';
    UserName: string = '';
    Password: string | null = null;
    LastLoginTime: string | null = null;
    ContactDTOList: ContactDTOModel[] = [];
    AddressDTOList: AddressDTOModel[] = [];
    ProfileContentHistoryDTOList: any = null;
    OptInPromotions: boolean = false;
    OptInPromotionsMode: string | null = null;
    OptInLastUpdatedDate: string | null = null;
    PolicyTermsAccepted: boolean = false;
    IsActive: boolean = true;
    CreatedBy: string = '';
    CreationDate: string = '';
    LastUpdatedBy: string = '';
    LastUpdateDate: string = '';
    SiteId: number = -1;
    MasterEntityId: number = -1;
    SynchStatus: boolean = false;
    Guid: string = '';
    ExternalSystemReference: string = '';
    OptOutWhatsApp: boolean = false;
    UserStatus: string | null = null;
    PasswordChangeDate: string | null = null;
    InvalidAccessAttempts: number = 0;
    LockedOutTime: string | null = null;
    PasswordChangeOnNextLogin: boolean = false;
    IsChanged: boolean = false;
    IsChangedRecursive: boolean = false;

}

export class CustomDataSetDTOModel {
    CustomDataSetId: number = -1;
    Dummy: string = '';
    CreatedBy: string | null = null;
    CreationDate: string = '0001-01-01T00:00:00';
    LastUpdatedBy: string | null = null;
    LastUpdateDate: string = '0001-01-01T00:00:00';
    SiteId: number = -1;
    MasterEntityId: number = -1;
    SynchStatus: boolean = false;
    Guid: string | null = null;
    IsChanged: boolean = true;
    IsChangedRecursive: boolean = true;
    CustomDataDTOList: any[] = [];

}
