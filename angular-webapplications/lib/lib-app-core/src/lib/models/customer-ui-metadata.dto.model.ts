/**
 * @fileoverview Customer UI metadata DTO model
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
 */
import { BaseDTO } from 'lib-app-core';

export class CustomerUIMetadataContainerDTOModel extends BaseDTO<CustomerUIMetadataContainerDTOModel> {
    CustomerFieldOrder: number = 0;
    CustomAttributeFlag: number = 0;
    CustomAttributeId: number = -1;
    CustomerFieldName: string = '';
    EntityFieldCaption: string = '';
    CustomerFieldValues: string[] | null = null;
    EntityFieldName: string = '';
    CustomerFieldValue: string = '';
    CustomerFieldType: string = '';
    ValidationType: string = '';
    FieldLength: string = '0';
    DisplayFormat: string | null = null;
    CustomAttributesContainerDTO: CustomAttributesContainerDTO =
        new CustomAttributesContainerDTO();
    constructor() {
        super();
    }
}

export class CustomAttributesContainerDTO {
    CustomAttributeId: number = -1;
    Name: string | null = null;
    Sequence: number = 0;
    Type: string | null = null;
    Applicability: string | null = null;
    Access: string | null = null;
    CustomAttributeValueListContainerDTOList: CustomAttributeValueListContainerDTO[] =
        [];
}

export class CustomAttributeValueListContainerDTO {
    ValueId: number = 0;
    Value: string = '';
    CustomAttributeId: number = 0;
    IsDefault: string = '';
}
