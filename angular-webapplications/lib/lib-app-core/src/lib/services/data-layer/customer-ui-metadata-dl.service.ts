/**
 * @fileoverview Customer UI metadata data layer service
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
 */
import { Injectable } from '@angular/core';
import {
    ApiServiceBase,
    CustomerUIMetadataContainerDTOModel,
    extractDTOList,
} from 'lib-app-core';
import { waiverConstants } from 'projects/online-waiver/src/app/constants/waiver.constant';
import { Observable, tap } from 'rxjs';

export type CustomerUIMetadataApiParams = { 
    siteId: number;
    languageId: number;
}
@Injectable()
export class CustomerUIMetadataServiceDL extends ApiServiceBase {
    private _apiData: CustomerUIMetadataContainerDTOModel[] = [];
    private _apiParams!: CustomerUIMetadataApiParams;

    constructor() {
        super(
            waiverConstants.TRANSFER_STATE_KEYS.CUSTOMER_UI_METADATA,
            'CUSTOMER_UI_METADATA_JSON'
        );
        this.init();
    }

    buildApiParams(data: CustomerUIMetadataApiParams) {
        this._apiParams = data;
    }

    load(): Observable<{ data: CustomerUIMetadataContainerDTOModel[] }> {
        const url = this.getJsonApiUrl(this._apiParams);
        return this._http.get<{ data: CustomerUIMetadataContainerDTOModel[] }>(url).pipe(
            tap((response) => {
                const customerUIMetadataContainerDTOList =
                    extractDTOList<CustomerUIMetadataContainerDTOModel>(
                        response,
                        waiverConstants.CUSTOMER_UI_METADATA_DTO_LIST
                    );
                this._apiData = customerUIMetadataContainerDTOList;
                this.storeData(this._apiData);
            })
        );
    }
}
