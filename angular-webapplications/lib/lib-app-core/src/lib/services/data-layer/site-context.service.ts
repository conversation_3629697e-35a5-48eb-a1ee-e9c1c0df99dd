/**
 * @fileoverview Site context service to get the site id, language id and user id
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */

import { inject, Injectable, signal } from '@angular/core';
import { CookieService, DEFAULT_APP_CONFIG_TOKEN } from 'lib-app-core';

@Injectable({ providedIn: 'root' })
export class SiteContextService {
    private _defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);
    private _cookieService = inject(CookieService);
    private _siteId = signal<number>(this._defaultAppConfig['siteId']);
    private _languageId = signal<number>(this._defaultAppConfig['languageId']);
    private _userId = signal<string | null>(
        this._cookieService.getCookie('userId')
    );

    get siteId(): number {
        return this._siteId();
    }

    set siteId(value: number) {
        this._siteId.set(value);
    }

    get languageId(): number {
        return this._languageId();
    }

    set languageId(value: number) {
        this._languageId.set(value);
    }

    get userId(): string | null {
        return this._userId();
    }

    set userId(value: string | null) {
        this._userId.set(value);
    }
}
