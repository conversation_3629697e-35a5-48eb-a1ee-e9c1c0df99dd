/**
 * @fileoverview Country container data layer service
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
 */
import { Injectable } from '@angular/core';
import {
    ApiServiceBase,
    CountryContainerDTOModel,
    extractDTOList,
    appCoreConstant,
} from 'lib-app-core';
import { Observable, tap } from 'rxjs';

export type CountryContainerApiParams = {
    siteId: number
}

@Injectable()
export class CountryContainerServiceDL extends ApiServiceBase {
    private _apiParams!: CountryContainerApiParams;
    private _apiData: CountryContainerDTOModel[] = [];

    constructor() {
        super(
            appCoreConstant.TRANSFER_STATE_KEYS.COUNTRY_CONTAINER_DATA,
            'COUNTRY_CONTAINER_JSON'
        );
        this.init();
    }

    buildApiParams(data: CountryContainerApiParams) {
        this._apiParams = data
    }

    load(): Observable<{ data: CountryContainerDTOModel[] }> {
        const url = this.getJsonApiUrl(this._apiParams);

        return this._http.get<{ data: CountryContainerDTOModel[] }>(url).pipe(
            tap((response) => {
                this._apiData = extractDTOList<CountryContainerDTOModel>(
                    response,
                    appCoreConstant.COUNTRY_CONTAINER_DTO_LIST
                );
                this.storeData(this._apiData);
            })
        );
    }
}
