/**
 * @fileoverview Rich contents data layer service
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase, RichContentContainerDTOModel } from 'lib-app-core';
import { Observable } from 'rxjs';
import { appCoreConstant } from '../../constants/app-core.constant';

@Injectable()
export class RichContentsServiceDL extends ApiServiceBase {

    constructor() {
        super(
            appCoreConstant.TRANSFER_STATE_KEYS.RICH_CONTENTS_DATA,
            'RICH_CONTENTS'
        );
        this.init();
    }

    load(): Observable<RichContentContainerDTOModel> {
        const url = this.getApiUrl();
        return this._http.get<RichContentContainerDTOModel>(url)
    }
} 