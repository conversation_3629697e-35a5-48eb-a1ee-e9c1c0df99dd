/**
 * @fileoverview Upload profile picture data layer service
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
*/
import { Injectable } from "@angular/core";
import { ApiServiceBase } from "./api-service-base-dl.service";
import { Observable } from "rxjs";
import { appCoreConstant } from "../../constants/app-core.constant";

type UploadProfilePictureApiParams = {
    customerId: number;
}

type UploadProfilePictureBody = {
    ProfileImageBase64: string;
}

@Injectable()
export class UploadProfilePictureServiceDL extends ApiServiceBase {
    private _apiParams!: UploadProfilePictureApiParams;
    private _apiBody!: UploadProfilePictureBody;

    constructor() {
        super(appCoreConstant.TRANSFER_STATE_KEYS.UPLOAD_PROFILE_PICTURE, 'UPLOAD_PROFILE_PICTURE');
        this.init();
    }

    buildApiParams(data: UploadProfilePictureApiParams) {
        this._apiParams = data;
    }

    buildApiBody(data: UploadProfilePictureBody) {
        this._apiBody = data;
    }

    load(): Observable<any> {
        const url = this.getApiUrl(this._apiParams);
        return this._http.post(url, [this._apiBody]);
    }
}