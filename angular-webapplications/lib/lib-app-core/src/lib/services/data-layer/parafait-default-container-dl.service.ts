/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { appCoreConstant, extractDTOList } from 'lib-app-core';
import { Observable, tap } from 'rxjs';
import { ParafaitDefaultContainerDTOModel } from '../../models/parafait-default-container-dto.model';
import { ApiServiceBase } from './api-service-base-dl.service';

export type ParafaitDefaultContainerApiParams = {
    siteId: number
}

@Injectable({ providedIn: 'root' })
export class ParafaitDefaultContainerService extends ApiServiceBase {
    private _apiParams!: ParafaitDefaultContainerApiParams;
    private _apiData: any;
    constructor() {
        super(appCoreConstant.TRANSFER_STATE_KEYS.PARAFAIT_DEFAULT_CONTAINER_DATA, 'PARAFAIT_DEFAULT_CONTAINER_JSON')
        this.init();
    }

    buildApiParams(data: ParafaitDefaultContainerApiParams) {
        this._apiParams = data
    }

    load(): Observable<{ data: ParafaitDefaultContainerDTOModel[] }> {
        const url = this.getJsonApiUrl(this._apiParams);
        return this._http.get<{ data: ParafaitDefaultContainerDTOModel[] }>(url).pipe(
            tap(response => {
                const dtoList = extractDTOList<ParafaitDefaultContainerDTOModel>(response, appCoreConstant.PARAFAIT_DEFAULT_CONTAINER_DTO_LIST);

                this._apiData = ParafaitDefaultContainerDTOModel.fromList(dtoList);
                this.storeData(this._apiData); // Store only the list, or full response if needed

            })
        );
    }
}