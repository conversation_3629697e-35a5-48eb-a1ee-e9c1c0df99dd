import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Request, Response } from 'express';
import { REQUEST, RESPONSE } from '../../utils/injection-tokens';

export interface CookieOptions {
    expires?: Date | number;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
    httpOnly?: boolean;
}

@Injectable({ providedIn: 'root' })
export class CookieService {
    private readonly platformId = inject(PLATFORM_ID);
    private readonly request = inject<Request>(REQUEST, { optional: true });
    private readonly response = inject<Response>(RESPONSE, { optional: true });

    private isServer(): boolean {
        return isPlatformServer(this.platformId);
    }

    private isBrowser(): boolean {
        return isPlatformBrowser(this.platformId);
    }

    setCookie(
        name: string,
        value: string,
        options: CookieOptions = {
            path: '/',
            secure: true,
            sameSite: 'Strict',
            httpOnly: false
        }
    ): void {
        const cookieString = this.buildCookieString(name, value, options);
    
        if (this.isServer()) {
            // --- Server-side cookie setting ---
            if (!this.response || !this.request) return;
    
            const existing = this.response.getHeader('Set-Cookie');
            const cookieHeader = Array.isArray(existing)
                ? [...existing, cookieString]
                : existing
                ? [existing as string, cookieString]
                : [cookieString];
    
            this.response.setHeader('Set-Cookie', cookieHeader);
        } else {
            // --- Client-side cookie setting ---
            document.cookie = cookieString;
        }
    }
    
    private buildCookieString(name: string, value: string, options: CookieOptions): string {
        const parts = [`${name}=${encodeURIComponent(value)}`];
    
        if (options.expires) {
            const expiresDate =
                options.expires instanceof Date
                    ? options.expires
                    : new Date(options.expires);
            parts.push(`Expires=${expiresDate.toUTCString()}`);
        }
    
        if (options.path) parts.push(`Path=${options.path}`);
        if (options.domain) parts.push(`Domain=${options.domain}`);
        if (options.secure) parts.push(`Secure`);
        if (options.sameSite) parts.push(`SameSite=${options.sameSite}`);
        if (options.httpOnly && this.isServer()) parts.push(`HttpOnly`);
    
        return parts.join('; ');
    }
    
    getCookie(name: string): string | null {
        if (this.isServer()) {
            if (!this.request) return null;
            const cookieHeader = this.request.headers?.cookie || '';
            const cookies = this.parseCookies(cookieHeader);
            return cookies[name] || null;
        } else if (this.isBrowser()) {
            const nameEQ = name + '=';
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                cookie = cookie.trim();
                if (cookie.startsWith(nameEQ)) {
                    return decodeURIComponent(cookie.substring(nameEQ.length));
                }
            }
        }
        return null;
    }

    deleteCookie(name: string): void {
        if (this.isServer()) {
            if (this.response && this.request) {
                this.response.clearCookie(name);
            }
        } else if (this.isBrowser()) {
            const expiredDate = new Date(0);
            this.setCookie(name, '', { expires: expiredDate });
        }
    }

    hasCookie(name: string): boolean {
        return this.getCookie(name) !== null;
    }

    getAllCookies(): Record<string, string> {
        if (this.isServer()) {
            if (!this.request) return {};
            const cookieHeader = this.request.headers?.cookie || '';
            return this.parseCookies(cookieHeader);
        } else {
            const cookies: Record<string, string> = {};
            const cookieArray = document.cookie.split(';');
            for (let cookie of cookieArray) {
                const [rawName, ...rawValue] = cookie.trim().split('=');
                if (rawName && rawValue) {
                    cookies[decodeURIComponent(rawName)] = decodeURIComponent(rawValue.join('='));
                }
            }
            return cookies;
        }
    }

    getCookieAsObject<T = any>(name: string): T | null {
        const value = this.getCookie(name);
        try {
            return value ? JSON.parse(value) : null;
        } catch {
            return null;
        }
    }

    setCookieAsObject<T>(name: string, value: T, options: CookieOptions = {}): void {
        this.setCookie(name, JSON.stringify(value), options);
    }

    private parseCookies(cookieHeader: string): Record<string, string> {
        return cookieHeader.split(';').reduce((acc, part) => {
            const [key, ...val] = part.trim().split('=');
            if (key && val) {
                acc[key] = decodeURIComponent(val.join('='));
            }
            return acc;
        }, {} as Record<string, string>);
    }
}
