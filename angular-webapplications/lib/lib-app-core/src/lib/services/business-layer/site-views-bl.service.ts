/**
 * @fileoverview Site views business layer service
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-16
 */

import { inject, Injectable, signal } from "@angular/core";
import { SiteViewsServiceDL } from "../data-layer/site-views-dl.service";
import { SiteViewsDTOModel } from "../../models/site-views-dto.model";
import { AppInitService } from "projects/online-waiver/src/app/core/app-init.service";
import { Router } from "@angular/router";

@Injectable()
export class SiteViewsServiceBL {
	private _siteViewsDL = inject(SiteViewsServiceDL);
	private _siteViews = signal<SiteViewsDTOModel[]>([]);
	private _appInitService = inject(AppInitService);
	private _router = inject(Router);
	readonly selectedSiteId = signal<number | null>(null);
	
	constructor() {
    	this.initializeSiteViews();
	}

  	get siteViews() {
    	return this._siteViews();
  	}


	/**
	 * Load the site views data
	 */
	private initializeSiteViews() {
    	this._siteViewsDL.subscribeToData((siteViews: SiteViewsDTOModel[]) => {
    		this._siteViews.set(siteViews.filter((site) => site.IsMasterSite === false));
    	});
	}

	/**
	 * Filter the site views
	 * @param searchTerm - The search term to filter the site views
	 * @returns The filtered site views
	 */
	filterSiteViews(searchTerm: string) {
		return this._siteViews().filter((site) => {
			const name = site.SiteName?.toLowerCase() || '';
			const address = site.SiteAddress?.toLowerCase() || '';
			return name.includes(searchTerm) || address.includes(searchTerm);
		});
	}

	async selectSite(siteId: number) {
        await this._appInitService.updateSiteId(siteId);
        this._router.navigate(['/auth/register'], { replaceUrl: true });
    }	

	async onSiteSelect(siteId: number) {
        this.selectedSiteId.set(siteId);
        try {
            await this.selectSite(siteId);
        } catch (error) {
            console.error('Error selecting site:', error);
        } finally {
            this.selectedSiteId.set(null);
        }
    }

}
