/**
 * @fileoverview
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-04-03
 */

import { InjectionToken } from '@angular/core';

// Create a token
export const PARENT_FUNCTION_TOKEN = new InjectionToken<any>('ParentFunctionToken');

export const DEFAULT_APP_CONFIG_TOKEN = new InjectionToken<Record<string, any>>('default_app_config_token');

export const APP_CONSTANTS_TOKEN = new InjectionToken<Record<string, any>>('app_constants_token');

/**
 * SSR-only DI tokens for passing the current Express Request/Response into Angular.
 *
 * Why InjectionTokens?
 * - Angular’s DI needs a key to resolve values. Express `Request`/`Response` are not
 *   Angular services and have no DI metadata, so we create opaque InjectionTokens.
 * - Keeps server-only concerns out of the browser bundle. The tokens are safe to import
 *   on the client (they’re just identifiers), while the actual Express objects are only
 *   provided on the server.
 * - Lets server-side services (e.g., CookieService) `inject()`
 *   the current request/response cleanly and with types.
 *
 * Availability:
 * - These tokens are provided per HTTP request during SSR (see BaseServer).
 * - They resolve ONLY on the server. In the browser they are undefined, so always inject
 *   them as optional and guard with `isPlatformServer()`.
 *
 * Typical Uses:
 * - REQUEST: read headers, cookies, URL, locale, user-agent, etc.
 * - RESPONSE: set status/headers, redirects, `Set-Cookie`, cache-control, etc.
 */

export const REQUEST = new InjectionToken<Request>('REQUEST');

export const RESPONSE = new InjectionToken<Response>('RESPONSE');

// export const APP_ENVIRONMENT = new InjectionToken<Record<string, any>>('APP_ENVIRONMENT');
