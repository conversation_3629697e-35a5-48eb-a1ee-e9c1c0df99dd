import { signal, computed, DestroyRef, inject } from '@angular/core';
import { finalize, Observable, takeUntil, Subject } from 'rxjs';

export class RequestState {
    /**
     * RequestState is a helper base class to manage loading and error states
     * for async operations in Angular services or components.
     *
     * Features:
     * - Tracks loading state across multiple API calls.
     * - Stores and exposes error messages.
     * - Provides a reusable method to handle requests with built-in loading and error management.
     * - Auto-unsubscribes when the component/service is destroyed.
     *
     * Usage:
     * class MyService extends RequestState {
     *   fetchData() {
     *     this._handleRequest(
     *       this.api.getData(),
     *       (res) => { this.data.set(res); },
     *       (err) => { console.error(err); }
     *     );
     *   }
     * }
     *
     * In component:
     * protected myService = inject(MyService);
     *
     * In template:
     * @if(myService.loading()) {
     * <div>Loading...</div>
     * }
     * @if(myService.errorMessage()) {
     * <div>Error: {{ myService.errorMessage() }}</div>
     * }
     */

    private _loadingCount = signal(0);
    private destroyRef = inject(DestroyRef);
    private destroy$ = new Subject<void>();

    constructor() {
        this.destroyRef.onDestroy(() => {
            this.destroy$.next();
            this.destroy$.complete();
        });
    }

    /**
     * Returns a signal for the error message.
     */
    errorMessage = signal<string | null>(null);

    /**
     * Returns a computed signal for the loading state.
     */
    loading = computed(() => this._loadingCount() > 0);

    /**
     * Handles an Observable request, tracking global loading state.
     *
     * Increments the loading counter when the request starts and decrements it when the request completes (success or error).
     * Error handling and error state management are delegated to the provided onError callback.
     * Automatically unsubscribes when the component/service is destroyed.
     *
     * @param request$ - The Observable representing the async request.
     * @param onSuccess - Callback invoked with the response on success.
     * @param onError - Optional callback invoked with the error on failure. Responsible for error state management.
     */
    protected _handleRequest(
        request$: Observable<any>,
        onSuccess: (response: any) => void,
        onError?: (error: any) => void
    ) {
        this._loadingCount.set(this._loadingCount() + 1);
        this.errorMessage.set(null);

        request$
            .pipe(
                takeUntil(this.destroy$),
                finalize(() => {
                    this._loadingCount.set(
                        Math.max(this._loadingCount() - 1, 0) // Ensure loading count doesn't go below 0
                    );
                })
            )
            .subscribe({
                next: onSuccess,
                error: (err) => {
                    const message = this.formatErrorMessage(
                        err?.error?.data || // standard JS Error or HttpErrorResponse
                        'Something went wrong' // fallback error message
                    );
                    this.errorMessage.set(message);
                    onError?.(err);
                },
            });
    }

    /**
     * Formats the error message by stripping the error code (e.g., "CU_00005 - ") if present.
     * @param raw - The raw error message.
     * @returns The formatted error message.
     */
    private formatErrorMessage(raw: string): string {
        if(typeof raw !== 'string') return raw;
        const parts = raw?.split(' - ');
        return parts?.length > 1 ? parts[1].trim() : raw?.trim();
    }
}