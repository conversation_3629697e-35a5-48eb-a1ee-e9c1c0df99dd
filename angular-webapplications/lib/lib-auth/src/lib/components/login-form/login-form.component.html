<div class="flex flex-col gap-5">
    <p class="text-sm text-neutral-dark">
        Add the below details to get an account. We don't share or sell your
        data.
    </p>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="flex flex-col space-y-4 max-w-[23rem]">
            <!-- Email Address -->
            <lib-text-input formControlName="email" id="email" label="Email Address" type="email" placeholder="Enter email address" [required]="true" [errorMessages]="errorMessages.email"></lib-text-input>

            <!-- Password -->
            <lib-text-input formControlName="password" id="password" label="Password" type="password" placeholder="Enter password" [required]="true" [showPasswordToggle]="true" [errorMessages]="errorMessages.password"></lib-text-input>

            <!-- Forgot Password  -->
            <button
                class="self-start text-blue-500 underline"
                (click)="showForgotPassword.set(true)"
                type="button"
            >
                Forgot password?
            </button>
            @if(loginErrorMessage()) {
            <div class="max-w-[23rem] text-center mt-2 text-sm text-red-500">
                {{loginErrorMessage()}}
            </div>
        }
        </div>

        <!-- Desktop CTA -->
        <div class="hidden md:block">
            <hr class="my-4 text-surface" />
            <ng-container *ngTemplateOutlet="loginCTA"></ng-container>
        </div>
    </form>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <ng-container *ngTemplateOutlet="loginCTA"></ng-container>
</lib-page-footer>


<ng-template #loginCTA>
    <div class="flex flex-col md:flex-row items-center gap-4">
        <!-- Login Button -->
        <button type="button" [disabled]="loginForm.invalid  || loading()" (click)="onSubmit()"
            class="w-full max-w-[23rem] py-3 px-4 text-white font-medium bg-primary rounded-4xl disabled:bg-surface">
            {{ loading() ? "Logging in..." : "Login" }}
        </button>
        <p class="text-sm text-center text-primary">
            Don't have an account?
            <a routerLink="/auth/register" class="text-secondary-blue underline">Sign up</a>
        </p>  
    </div>
</ng-template>

<!-- Forgot Password Modal -->
<ng-template #forgotPasswordContent>
    <lib-forgot-password />
</ng-template>

<lib-modal
    [isOpen]="showForgotPassword()"
    [modalContent]="forgotPasswordContent"
    (closeModal)="closeForgotPassword()"
>
</lib-modal>
