/**
 * @fileoverview Business layer service for handling user login functionality
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable, signal } from '@angular/core';
import { LibAuthLoginDL } from '../data-layer/lib-auth-login-dl.service';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';
import { AuthenticateSystemUsersDTOModel } from 'lib-app-core';
import { Router } from '@angular/router';

@Injectable()
export class LibAuthLoginBL extends RequestState {
    private _libAuthLoginDL = inject(LibAuthLoginDL);
    loginData = signal<AuthenticateSystemUsersDTOModel | null>(null);
    private _cookieService = inject(CookieService);
    private _router = inject(Router);
    /**
     * Handles the login process by building API parameters,
     * invoking the Data Layer service, and managing the request state.
     *
     * @param userName - The username for login.
     * @param password - The password for login.
     */
    login(userName: string, password: string) {
        this._libAuthLoginDL.buildApiParams({
            UserName: userName,
            Password: password,
        });
        const login$ = this._libAuthLoginDL.load();
        this._handleRequest(login$, (response) => {
            this.loginData.set(AuthenticateSystemUsersDTOModel.fromSingle(response.data));
            this._cookieService.setCookie('userId', response.data.UserId,{ path: '/' });
            this._router.navigate([`/waivers/my-signed-waivers`]);            
        });
    }
}
