/**
 * @fileoverview Wrapper service for cryptographic operations
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin, map } from 'rxjs';
import { DEFAULT_APP_CONFIG_TOKEN, EnvService } from 'lib-app-core';
import CryptoES, { WordArray } from 'crypto-es';
import { JSEncrypt } from 'jsencrypt';

/**
 * Configuration interface for RSA key retrieval
 */
export interface RsaKeyConfig {
    siteId: string;
    application: string;
    version: string;
    identifier: string;
    format: string;
}

/**
 * Result interface for encryption initialization
 */
export interface CryptoInitResult {
    success: boolean;
    error?: string;
}

/**
 * Clean interface for cryptographic operations without exposing third-party types
 */
export interface ICryptoWrapper {
    initializeKeys(): Observable<CryptoInitResult>;
    generateSymmetricKey(bits?: number): void;
    encryptSymmetric(message: string): string;
    encryptAsymmetric(message: string): string;
    getSymmetricKeyAsBase64(): string;
    getServerTimestamp(): number | null;
}

@Injectable({
    providedIn: 'root'
})
export class CryptoWrapperService implements ICryptoWrapper {
    private http = inject(HttpClient);
    private envService = inject(EnvService);
    private defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);

    private symmetricKey: WordArray | null = null;
    private asymmetricEncryption: JSEncrypt | null = null;
    private serverTimestamp: number | null = null;

    /**
     * Initializes both RSA and retrieves server timestamp
     * @returns Observable with initialization result
     */
    initializeKeys(): Observable<CryptoInitResult> {
        this.asymmetricEncryption = new JSEncrypt();

        return forkJoin({
            time: this.fetchServerTimestamp(),
            key: this.fetchRsaPublicKey(),
        }).pipe(
            map(() => ({ success: true })),
            // Handle errors gracefully
            map((result) => result || { success: false, error: 'Initialization failed' })
        );
    }

    /**
     * Generates a new symmetric encryption key
     * @param bits - Key size in bits (default: 256)
     */
    generateSymmetricKey(bits: number = 256): void {
        this.symmetricKey = CryptoES.lib.WordArray.random(bits / 8);
    }

    /**
     * Encrypts a message using symmetric encryption (AES)
     * @param message - Plain text message to encrypt
     * @returns Base64 encoded encrypted string
     * @throws Error if symmetric key is not initialized
     */
    encryptSymmetric(message: string): string {
        if (!this.symmetricKey) {
            throw new Error('Symmetric key not initialized. Call generateSymmetricKey() first.');
        }

        const iv = CryptoES.enc.Hex.parse('00000000000000000000000000000000');
        const encrypted = CryptoES.AES.encrypt(message, this.symmetricKey, {
            iv,
            mode: CryptoES.mode.CBC,
            padding: CryptoES.pad.Pkcs7,
        });

        if (!encrypted.ciphertext) {
            throw new Error('Symmetric encryption failed: ciphertext is undefined');
        }

        return encrypted.ciphertext.toString(CryptoES.enc.Base64);
    }

    /**
     * Encrypts a message using asymmetric encryption (RSA)
     * @param message - Plain text message to encrypt
     * @returns Encrypted string
     * @throws Error if RSA encryption is not initialized
     */
    encryptAsymmetric(message: string): string {
        if (!this.asymmetricEncryption) {
            throw new Error('Asymmetric encryption not initialized. Call initializeKeys() first.');
        }

        const result = this.asymmetricEncryption.encrypt(message);
        if (!result) {
            throw new Error('Asymmetric encryption failed');
        }

        return result;
    }

    /**
     * Gets the symmetric key as a Base64 string
     * @returns Base64 encoded symmetric key
     * @throws Error if symmetric key is not initialized
     */
    getSymmetricKeyAsBase64(): string {
        if (!this.symmetricKey) {
            throw new Error('Symmetric key not initialized');
        }
        return this.symmetricKey.toString(CryptoES.enc.Base64);
    }

    /**
     * Gets the server timestamp
     * @returns Server timestamp in seconds since epoch, or null if not fetched
     */
    getServerTimestamp(): number | null {
        return this.serverTimestamp;
    }

    /**
     * Fetches server timestamp from the API
     * @private
     */
    private fetchServerTimestamp(): Observable<void> {
        const url = `${this.envService.parafaitApiBaseUrl}/Login/SecondsSinceEpoch`;
        return this.http.get<{ data: number }>(url).pipe(
            map((res) => {
                this.serverTimestamp = res.data;
            })
        );
    }

    /**
     * Fetches RSA public key from the API and configures the encryption instance
     * @private
     */
    private fetchRsaPublicKey(): Observable<void> {
        const url = `${this.envService.parafaitApiBaseUrl}/Login/PublicKey`;
        const params: Record<string, string> = {
            siteId: this.defaultAppConfig['siteId']?.toString() || '1010',
            application: 'WaiverWebsite',
            version: '*********',
            identifier: 'WaiverWebsite',
            format: 'PEM',
        };

        return this.http.get<{ data: string }>(url, { params }).pipe(
            map((res) => {
                if (!this.asymmetricEncryption) {
                    this.asymmetricEncryption = new JSEncrypt();
                }
                this.asymmetricEncryption.setPublicKey(res.data);
            })
        );
    }
} 