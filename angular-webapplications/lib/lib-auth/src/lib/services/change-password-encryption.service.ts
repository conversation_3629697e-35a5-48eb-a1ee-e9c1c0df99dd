/**
 * @fileoverview Service for encrypting change password requests
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { EncryptionBaseService } from './encryption-service-base.service';
import CryptoES from 'crypto-es';

/**
 * Configuration interface for change password encryption process
 * Contains all necessary parameters for encrypting change password credentials
 */
export interface ChangePasswordEncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
    userName: string;
    currentPassword: string;
    newPassword: string;
    machineName: string;
}

/**
 * Interface for the encrypted change password request payload
 * Contains all encrypted data and metadata for password change
 */
export interface EncryptedChangePasswordRequest {
    EncryptedUserName: string;
    EncryptedPassword: string;
    EncryptedNewPassword: string;
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedKeyMaterial: string;
}

@Injectable()
export class ChangePasswordEncryptionService extends EncryptionBaseService {
    /**
     * Encrypts change password credentials using hybrid encryption (AES + RSA)
     *
     * Process:
     * 1. Initializes encryption setup (RSA public key, server time)
     * 2. Generates AES key for symmetric encryption
     * 3. Encrypts sensitive data (username, current password, new password, machine name) with AES
     * 4. Encrypts AES key material (key + timestamp) with RSA
     * 5. Returns complete encrypted payload
     *
     * @param config - Change password encryption configuration containing credentials and metadata
     * @returns Observable of encrypted change password request payload
     */
    override encrypt(
        config: ChangePasswordEncryptionConfig
    ): Observable<EncryptedChangePasswordRequest> {
        this.buildEncryptionParams(config);

        return this.initializeEncryption().pipe(
            map(() => {
                if (!this.aesKey) this.generateAESKey(256);
                const base64AesKey = this.aesKey!.toString(CryptoES.enc.Base64);
                const keyMaterial = `${base64AesKey}|${this.secondsSinceEpoch}`;
                const encryptedKeyMaterial = this.rsaEncrypt(keyMaterial);

                const payload: EncryptedChangePasswordRequest = {
                    EncryptedUserName: this.aesEncrypt(config.userName),
                    EncryptedPassword: this.aesEncrypt(config.currentPassword),
                    EncryptedNewPassword: this.aesEncrypt(config.newPassword),
                    Application: config.applicationName,
                    Version: config.applicationVersion,
                    Identifier: config.applicationIdentifier,
                    SiteId: config.siteId,
                    EncryptedKeyMaterial: encryptedKeyMaterial,
                };

                return payload;
            })
        );
    }
} 