/**
 * @fileoverview CustomerRelationBL is a service that provides business logic for the customer relation
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { inject, Injectable } from '@angular/core';
import { Observable, map, catchError, of, startWith } from 'rxjs';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { RelatedCustomerDL } from '../data-layer/related-customer-dl.service';
import { PrimaryCustomerDL } from '../data-layer/primary-customer-dl.service';
import { PrimaryCustomerDataDTOModel } from '../../models/primary-customer-data.model';
import { CustomerRelationshipDTO } from '../../models/customer-relationship-dto.model';
import { UserLoginDTOModel } from 'lib/lib-auth/src/lib/models/user-login-dto.model';
import {
    CustomerDataResponse,
    ILoadingState,
    IParticipant,
    RelatedCustomerResponse,
} from '../../interfaces/customer.interface';
import { customerValidation } from '../../constants/customer.constant';
import { CookieService } from 'lib-app-core';

@Injectable()
export class CustomerRelationBL extends RequestState {
    private _relatedCustomerDL = inject(RelatedCustomerDL);
    private _primaryCustomerDL = inject(PrimaryCustomerDL);
    private _cookieService = inject(CookieService);
    private customerId = this._cookieService.getCookie('userId') ?? '';

    /**
     * Transforms raw customer data to IParticipant format
     */
    private transformToParticipant(
        customerDTO: UserLoginDTOModel
    ): IParticipant {
        const { FirstName, MiddleName, LastName, DateOfBirth } = customerDTO;
        const fullName = [FirstName, MiddleName, LastName].filter(Boolean);

        return {
            firstName: fullName[0] ?? 'Unknown',
            lastName: fullName.slice(1).join(' '),
            dateOfBirth: DateOfBirth || '',
            type: 'primary',
            isSigned: true,
        };
    }

    /**
     * Transforms related customer data to IParticipant format
     */
    private transformRelatedCustomer(
        raw: CustomerRelationshipDTO
    ): IParticipant {
        const dto = CustomerRelationshipDTO.fromSingle(raw);
        const [firstName, ...rest] = (dto.RelatedCustomerName ?? '').split(' ');

        return {
            firstName: firstName || 'Unknown',
            lastName: rest.join(' '),
            dateOfBirth: dto.RelatedCustomerDTO?.DateOfBirth || '',
            type: 'minor',
            isSigned: false,
        };
    }

    /**
     * Creates error state for failed requests
     */
    private createErrorState(message: string): ILoadingState<never> {
        return {
            loading: false,
            data: null,
            error: message,
        };
    }

    /**
     * Gets the primary customer data
     *
     * This method is used to get the primary customer data from the database
     *
     * @returns Observable<ILoadingState<IParticipant>> - The primary customer data
     */
    getPrimaryCustomerData(): Observable<ILoadingState<IParticipant>> {
        this._primaryCustomerDL.buildApiParams({ customerId: this.customerId });

        return this._primaryCustomerDL.load().pipe(
            startWith({
                loading: true,
                data: null,
                error: null,
            } as ILoadingState<IParticipant>),
            map(
                (
                    response: CustomerDataResponse | ILoadingState<IParticipant>
                ): ILoadingState<IParticipant> => {
                    if ('loading' in response && response.loading) {
                        return response;
                    }

                    if (Array.isArray(response.data)) {
                        const rawData = response.data[0];
                        if (!rawData) {
                            return this.createErrorState(
                                customerValidation.NO_PRIMARY_CUSTOMER_DATA
                            );
                        }

                        //Type guard to ensure response is CustomerDataResponse
                        if ('totalPages' in response) {
                            const customerDTO =
                                PrimaryCustomerDataDTOModel.fromSingle({
                                    data: [rawData],
                                    customersImage: response.customersImage,
                                    customersIdImage: response.customersIdImage,
                                    totalPages: response.totalPages,
                                });

                            return {
                                loading: false,
                                data: this.transformToParticipant(
                                    customerDTO.data[0]
                                ),
                                error: null,
                            };
                        }
                    }

                    return this.createErrorState(
                        customerValidation.CUSTOMER_RELATION_ERROR
                    );
                }
            ),
            catchError((error: unknown) => {
                console.error(
                    customerValidation.ERROR_LOADING_PRIMARY_CUSTOMER_DATA,
                    error
                );
                return of(
                    this.createErrorState(
                        customerValidation.ERROR_LOADING_PRIMARY_CUSTOMER_DATA
                    )
                );
            })
        );
    }

    /**
     * Gets the related customer data
     *
     * This method is used to get the related customer data from the database
     *
     * @returns Observable<ILoadingState<IParticipant[]>> - The related customer data
     */
    getRelatedCustomerData(): Observable<ILoadingState<IParticipant[]>> {
        this._relatedCustomerDL.buildApiParams({ customerId: this.customerId });

        return this._relatedCustomerDL.load().pipe(
            startWith({
                loading: true,
                data: null,
                error: null,
            } as ILoadingState<IParticipant[]>),
            map(
                (
                    response:
                        | RelatedCustomerResponse
                        | ILoadingState<IParticipant[]>
                ): ILoadingState<IParticipant[]> => {
                    if ('loading' in response && response.loading) {
                        return response;
                    }

                    if ('CustomerRelationshipDTO' in response) {
                        const rawList = response.CustomerRelationshipDTO ?? [];
                        const participants = rawList.map((raw) =>
                            this.transformRelatedCustomer(raw)
                        );

                        return {
                            loading: false,
                            data: participants,
                            error: null,
                        };
                    }

                    return this.createErrorState(
                        customerValidation.CUSTOMER_RELATION_ERROR
                    );
                }
            ),
            catchError((error: unknown) => {
                console.error(
                    customerValidation.ERROR_LOADING_RELATED_CUSTOMER_DATA,
                    error
                );
                return of(
                    this.createErrorState(
                        customerValidation.ERROR_LOADING_RELATED_CUSTOMER_DATA
                    )
                );
            })
        );
    }
}
