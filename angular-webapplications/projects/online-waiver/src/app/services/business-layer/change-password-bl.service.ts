/**
 * @fileoverview Business layer service for handling change password functionality
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { RequestState } from 'lib-app-core';
import { ChangePasswordDL, ChangePasswordRequest } from '../data-layer/change-password-dl.service';

@Injectable()
export class ChangePasswordBL extends RequestState {
    private _changePasswordDL = inject(ChangePasswordDL);

    /**
     * Handles the change password process by building API parameters,
     * invoking the Data Layer service, and managing the request state.
     *
     * @param request - The change password request data
     * @param onSuccess - Optional callback for successful password change
     */
    changePassword(request: ChangePasswordRequest, onSuccess?: () => void) {
        this._changePasswordDL.buildApiParams(request);
        const changePassword$ = this._changePasswordDL.load();
        
        this._handleRequest(changePassword$, (response) => {
            if (onSuccess) {
                onSuccess();
            }
        });
    }
}
