/**
 * @fileoverview Data layer service for handling change password API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { ApiServiceBase, DEFAULT_APP_CONFIG_TOKEN } from 'lib-app-core';
import { Observable, switchMap } from 'rxjs';
import { ChangePasswordEncryptionConfig, ChangePasswordEncryptionService } from 'lib/lib-auth/src/lib/services/change-password-encryption.service';
import { LibUserDetailDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-user-details-dl.service';
import { SignWaiverService } from '../../core/sign-waiver.service';

export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
}

export interface ChangePasswordResponse {
    success: boolean;
    message: string;
}

@Injectable()
export class ChangePasswordDL extends ApiServiceBase {
    private _requestPayload!: ChangePasswordRequest;
    private readonly changePasswordEncryptionService = inject(ChangePasswordEncryptionService);
    private readonly defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);
    
    constructor() {
        super('change_password_data', 'CHANGE_PASSWORD');
        this.init();
    }

    /**
     * Builds the API parameters for the change password request
     * @param data - The change password request data
     */
    buildApiParams(data: ChangePasswordRequest) {
        this._requestPayload = data;
    }

    /**
     * Executes the change password API call with encrypted credentials
     *
     * Process:
     * 1. Constructs the API URL for change password endpoint
     * 2. Creates encryption configuration with user credentials and app metadata
     * 3. Encrypts the change password data using hybrid encryption (AES + RSA)
     * 4. Sends encrypted payload to the server
     *
     * @returns Observable of the server response containing change password result
     */
    load(): Observable<ChangePasswordResponse> {
        const url = `${this.getApiUrl()}`;

        // Create encryption config using the ChangePasswordEncryptionConfig interface
        const encryptionConfig: ChangePasswordEncryptionConfig = {
            siteId: this.defaultAppConfig['siteId'] || 1010,
            applicationName: 'OnlineWaiver',
            applicationVersion: '1.0.0',
            applicationIdentifier: 'waiver-app',
            userName: '<EMAIL>', // Get from user session/cookies
            currentPassword: this._requestPayload.currentPassword,
            newPassword: this._requestPayload.newPassword,
            machineName: this.defaultAppConfig['machineName'] || 'WebClient',
        };

        return this.changePasswordEncryptionService
            .encrypt(encryptionConfig)
            .pipe(
                switchMap((encryptedPayload) =>
                    this._http.post<ChangePasswordResponse>(url, encryptedPayload)
                )
            );
    }
}
