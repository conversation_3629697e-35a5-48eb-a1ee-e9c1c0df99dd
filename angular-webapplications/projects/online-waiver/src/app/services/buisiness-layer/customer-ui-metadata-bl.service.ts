import { computed, effect, inject, Injectable, signal } from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    ValidatorFn,
    Validators,
} from '@angular/forms';
import {
    areEqual,
    CountryContainerDTOModel,
    CountryContainerServiceDL,
    CustomAttributesContainerDTO,
    CustomAttributeValueListContainerDTO,
    CustomerUIMetadataContainerDTOModel,
    CustomerUIMetadataServiceDL,
    ParafaitDefaultContainerDTOModel,
    ParafaitDefaultContainerService,
    RequestState,
    SiteContextService,
} from 'lib-app-core';
import {
    Country,
    DropdownOption,
    DynamicFormField,
    ErrorMessage,
    FormValidatorService,
} from 'lib-ui-kit';
import { CreateCustomerServiceDL } from 'lib/lib-app-core/src/lib/services/data-layer/create-customer-dl.service';
import { waiverConstants } from '../../constants/waiver.constant';
import { CreateCustomerPayloadBuilderService } from './create-customer-payload-builder.service';
import { LibAuthLoginBL } from 'lib-auth';

export type Message = Record<string, string>;

type CustomerType =
    (typeof waiverConstants.CUSTOMER_TYPE)[keyof typeof waiverConstants.CUSTOMER_TYPE];
type InputFieldType =
    (typeof waiverConstants.INPUT_FIELD_TYPE)[keyof typeof waiverConstants.INPUT_FIELD_TYPE];

@Injectable()
export class CustomerUIMetadataServiceBL extends RequestState {
    /**
     * Business Layer service for managing customer UI metadata.
     *
     * This service handles the dynamic form field configuration for customer registration,
     * service will fetch the customer UI metadata from the data layer and map the metadata to dynamic
     * form fields for both major and minor customers.
     *
     * Key responsibilities:
     * - Load customer UI metadata from the data layer
     * - Filter and sort form fields based on customer type
     * - Exclude specific fields for minor customers
     * - Map the metadata to dynamic form fields for both major and minor customers
     */

    /**
     * Customer type constants for distinguishing between major (adult) and minor (child) customers
     */
    private readonly CUSTOMER_TYPE = waiverConstants.CUSTOMER_TYPE;

    /**
     * Data layer service for customer UI metadata operations
     */
    private readonly _customerUIMetadataServiceDL = inject(
        CustomerUIMetadataServiceDL
    );
    private readonly _countryContainerServiceDL = inject(
        CountryContainerServiceDL
    );
    private readonly _formValidatorService = inject(FormValidatorService);
    private readonly _parafaitDefaultContainerServiceDL = inject(
        ParafaitDefaultContainerService
    );
    private readonly _createCustomerPayloadBuilderService = inject(
        CreateCustomerPayloadBuilderService
    );
    private readonly _createCustomerServiceDL = inject(CreateCustomerServiceDL);
    private readonly _siteContextService = inject(SiteContextService);
    private readonly _libAuthLoginBL = inject(LibAuthLoginBL);
    // private readonly _uploadProfilePictureServiceDL = inject(UploadProfilePictureServiceDL);
    /**
     * Reactive signal for dynamic form fields for minor customers
     */
    minorFormFields = signal<DynamicFormField[]>([]);

    /**
     * Reactive signal for dynamic form fields for major customers
     */
    majorFormFields = signal<DynamicFormField[]>([]);

    /**
     * Reactive signal for country container data
     */
    countryContainer = signal<CountryContainerDTOModel[]>([]);

    /**
     * Reactive signal for age of major customer
     */
    ageOfMajorCustomer = signal<number | null>(null);

    customerUIMetadata = signal<CustomerUIMetadataContainerDTOModel[]>([]);

    isUIDataLoading = computed(
        () =>
            this.isCustomerUIMetadataLoading() ||
            this.isCountryContainerLoading() ||
            this.isAgeOfMajorCustomerLoading()
    );
    isSubmitting = computed(() => this.loading());
    isCustomerUIMetadataLoading = signal<boolean>(false);
    isCountryContainerLoading = signal<boolean>(false);
    isAgeOfMajorCustomerLoading = signal<boolean>(false);
    isMetadataLoaded = signal<boolean>(false);
    countryCode = computed<Country[]>(() =>
        this.countryContainer().map((c) => {
            return {
                name: c.CountryName,
                dialCode: c.CountryCode,
                countryFlagIcon: '',
            };
        })
    );

    private readonly countryOptions = computed(() =>
        this.countryContainer().map((c) => ({
            label: c.CountryName,
            value: c.CountryId.toString(),
        }))
    );

    private readonly excludedMinorFieldsSet = new Set<string>(
        waiverConstants.EXCLUDED_MINOR_FIELDS
    );

    constructor() {
        super();
    }

    loadInitialData(): void {
        this.loadCountryContainer();
        this.setAgeOfMajorCustomer();
        effect(() => {
            if (
                this.countryContainer().length &&
                this.ageOfMajorCustomer() &&
                !this.isMetadataLoaded()
            ) {
                this.loadCustomerUIMetadata();
                this.isMetadataLoaded.set(true);
            }
        });
    }

    /**
     * Loads customer UI metadata from the data layer and populates the dynamic form fields for both major and minor customers.
     *
     * This method:
     * 1. Calls the data layer service to fetch metadata
     * 2. Extracts the DTO list from the response
     * 3. Maps the metadata to dynamic form fields for both major and minor customers
     * 4. Updates the reactive signals with the mapped fields
     *
     */
    loadCustomerUIMetadata(): void {
        this.isCustomerUIMetadataLoading.set(true);
        this._customerUIMetadataServiceDL.buildApiParams({
            siteId: this._siteContextService.siteId,
            languageId: this._siteContextService.languageId,
        });

        this._customerUIMetadataServiceDL.load().subscribe();

        this._customerUIMetadataServiceDL.subscribeToData(
            (response: CustomerUIMetadataContainerDTOModel[]) => {
                try {
                    this.setFields(response, this.CUSTOMER_TYPE.MAJOR);
                    this.setFields(response, this.CUSTOMER_TYPE.MINOR);
                    this.customerUIMetadata.set(response);
                } catch (error) {
                    console.error(
                        'Error processing customer UI metadata:',
                        error
                    );
                } finally {
                    this.isCustomerUIMetadataLoading.set(false);
                }
            }
        );
    }

    /**
     * Sets the age of major customer from the parafait default container.
     */
    setAgeOfMajorCustomer(): void {
        this.isAgeOfMajorCustomerLoading.set(true);
        this._parafaitDefaultContainerServiceDL.buildApiParams({
            siteId: this._siteContextService.siteId,
        });
        this._parafaitDefaultContainerServiceDL.load().subscribe();

        this._parafaitDefaultContainerServiceDL.subscribeToData(
            (response: ParafaitDefaultContainerDTOModel[]) => {
                try {
                    const ageOfMajorCustomer =
                        ParafaitDefaultContainerDTOModel.getValueByName(
                            response,
                            waiverConstants.AGE_OF_MAJORITY
                        );
                    this.ageOfMajorCustomer.set(
                        ageOfMajorCustomer ? parseInt(ageOfMajorCustomer) : 0
                    );
                } catch (error) {
                    console.error(
                        'Error setting age of major customer:',
                        error
                    );
                } finally {
                    this.isAgeOfMajorCustomerLoading.set(false);
                }
            }
        );
    }

    /**
     * Loads the country container data from the data layer and updates the countryContainer signal.
     */
    loadCountryContainer(): void {
        this.isCountryContainerLoading.set(true);
        this._countryContainerServiceDL.buildApiParams({
            siteId: this._siteContextService.siteId,
        });
        this._countryContainerServiceDL.load().subscribe();

        this._countryContainerServiceDL.subscribeToData(
            (response: CountryContainerDTOModel[]) => {
                try {
                    this.countryContainer.set(response);
                } catch (error) {
                    console.error('Error setting country container:', error);
                } finally {
                    this.isCountryContainerLoading.set(false);
                }
            }
        );
    }

    /**
     * Maps customer UI metadata container DTOs to dynamic form field configuration.
     *
     * @param {CustomerUIMetadataContainerDTOModel[]} customerUIMetadataContainerDTOList - Array of customer UI metadata containers
     * @param {CustomerType} customerType - Customer type ('major' or 'minor') to filter fields
     * @returns {DynamicFormField[]} Array of dynamic form field configurations
     */
    private mapToDynamicFormFields(
        customerUIMetadataContainerDTOList: CustomerUIMetadataContainerDTOModel[],
        customerType: CustomerType
    ): DynamicFormField[] {
        const fields: DynamicFormField[] = [];
        let id = 0;

        customerUIMetadataContainerDTOList.forEach((dto) => {
            let fieldName: string;
            let label: string;
            let options: DropdownOption[] | undefined;
            let fieldType: InputFieldType;
            let isMandatory: boolean;
            let maxLength: number | undefined;

            // Determine whether it's a custom attribute or regular field
            if (
                areEqual(dto.CustomAttributeFlag, 1) &&
                dto.CustomAttributesContainerDTO
            ) {
                const customAttr = dto.CustomAttributesContainerDTO;
                if (
                    !customAttr.Name ||
                    areEqual(customAttr.CustomAttributeId, -1)
                )
                    return;

                fieldName = customAttr.Name;
                label = customAttr.Name;
                fieldType = this.getFieldType(dto, fieldName);
                isMandatory = areEqual(
                    dto.ValidationType,
                    waiverConstants.VALIDATION_TYPE.MANDATORY
                );
                maxLength = this.parseFieldLength(dto.FieldLength);
                options = areEqual(fieldType, 'select')
                    ? this.getCustomAttributeOptions(customAttr)
                    : undefined;
            } else {
                fieldName = dto.CustomerFieldName;
                fieldType = this.getFieldType(dto, fieldName);
                label = dto.EntityFieldCaption;
                isMandatory = areEqual(
                    dto.ValidationType,
                    waiverConstants.VALIDATION_TYPE.MANDATORY
                );
                maxLength = this.parseFieldLength(dto.FieldLength);
                options = areEqual(fieldType, 'select')
                    ? this.getOptions(dto, fieldName)
                    : undefined;
            }

            const validators: ValidatorFn[] = this.getValidators(
                fieldType,
                customerType,
                isMandatory,
                maxLength,
                fieldName
            );

            fields.push({
                id: `${fieldName}-${id.toString()}-${customerType}`,
                fieldName,
                label,
                type: fieldType,
                required: isMandatory,
                placeholder: areEqual(fieldType, 'select')
                    ? `Select ${label}`
                    : `Enter ${label}`,
                options,
                max: maxLength && maxLength > -1 ? maxLength : undefined,
                validators: {
                    validatorFn: validators.length ? validators : undefined,
                    maxLength,
                },
                countryCodeOptions: this.countryCode(),
            });

            id++;
        });

        return fields;
    }

    /**
     * Parses field length from string to number
     */
    private parseFieldLength(
        fieldLength: string | null | undefined
    ): number | undefined {
        if (!fieldLength) return undefined;
        const parsed = parseInt(fieldLength);
        return isNaN(parsed) ? undefined : parsed;
    }

    /**
     * Checks if a field name is in the excluded minor fields list.
     *
     * @param {string} fieldName - The name of the field to check
     * @returns {boolean} true if the field is excluded for minors, false otherwise
     */
    private isExcludedMinorField(fieldName: string): boolean {
        return this.excludedMinorFieldsSet.has(fieldName);
    }

    /**
     * Sets dynamic form fields for customers with field exclusion and ordering.
     *
     * @param {CustomerUIMetadataContainerDTOModel[]} dtoList - Array of customer UI metadata container DTOs
     * @param {CustomerType} type - Customer type ('major' or 'minor') to filter fields
     * @returns {void}
     */
    private setFields(
        dtoList: CustomerUIMetadataContainerDTOModel[],
        type: CustomerType
    ): void {
        if (!dtoList?.length) return;

        // Filter regular fields for minors (custom attributes are not excluded for minors)
        const filteredDtoList = areEqual(type, this.CUSTOMER_TYPE.MINOR)
            ? dtoList.filter(
                  (dto) => !this.isExcludedMinorField(dto.CustomerFieldName)
              )
            : dtoList;

        const sortedDtoList = this.sortFieldsByOrder(filteredDtoList);
        const fields = this.mapToDynamicFormFields(sortedDtoList, type);

        if (areEqual(type, this.CUSTOMER_TYPE.MINOR)) {
            this.minorFormFields.set(fields);
        } else {
            this.majorFormFields.set(fields);
        }
    }

    /**
     * Sorts fields by their order using overridden order or default order
     * Only regular attributes are sorted, custom attributes maintain their original order
     */
    private sortFieldsByOrder(
        dtoList: CustomerUIMetadataContainerDTOModel[]
    ): CustomerUIMetadataContainerDTOModel[] {
        return [...dtoList].sort((a, b) => {
            // Skip sorting for custom attributes (CustomAttributeFlag = 1)
            if (
                areEqual(a.CustomAttributeFlag, 1) ||
                areEqual(b.CustomAttributeFlag, 1)
            ) {
                return 0;
            }

            const orderA =
                waiverConstants.CUSTOMER_FIELD_OVERRIDDEN_ORDER[
                    a.CustomerFieldName as keyof typeof waiverConstants.CUSTOMER_FIELD_OVERRIDDEN_ORDER
                ] ?? a.CustomerFieldOrder;
            const orderB =
                waiverConstants.CUSTOMER_FIELD_OVERRIDDEN_ORDER[
                    b.CustomerFieldName as keyof typeof waiverConstants.CUSTOMER_FIELD_OVERRIDDEN_ORDER
                ] ?? b.CustomerFieldOrder;
            return orderA - orderB;
        });
    }

    /**
     * Determines the input field type for a given DTO and field name.
     *
     * @param {CustomerUIMetadataContainerDTOModel} dto - The DTO containing field metadata
     * @param {string} fieldName - The name of the field
     * @returns {InputFieldType} The determined input field type
     */
    private getFieldType(
        dto: CustomerUIMetadataContainerDTOModel,
        fieldName: string
    ): InputFieldType {
        const fieldType: string = dto.CustomerFieldType.toLowerCase();
        if (areEqual(fieldType, 'text')) {
            if (fieldName.includes(waiverConstants.EMAIL)) return 'email';
            if (fieldName.includes(waiverConstants.PHONE_DEFAULT)) {
                return 'phone';
            }
        }

        if (areEqual(fieldType, 'list')) {
            return 'select';
        }

        if (areEqual(fieldType, 'flag')) {
            return 'checkbox';
        }

        return fieldType as InputFieldType;
    }

    /**
     * Generates an array of Angular validators for a given field.
     *
     * @param {InputFieldType} fieldType - The type of the input field
     * @param {CustomerType} customerType - The customer type ('major' or 'minor')
     * @param {boolean} isMandatory - Whether the field is mandatory
     * @param {number} [maxLength] - The maximum length of the field (optional)
     * @param {string} [fieldName] - The name of the field (optional)
     * @returns {ValidatorFn[]} Array of validator functions
     */
    private getValidators(
        fieldType: string,
        customerType: CustomerType,
        isMandatory: boolean,
        maxLength?: number,
        fieldName?: string
    ): ValidatorFn[] {
        const validators: ValidatorFn[] = [];

        if (isMandatory) validators.push(Validators.required);
        if (areEqual(fieldType, 'email')) validators.push(Validators.email);
        if (maxLength && maxLength > 0)
            validators.push(Validators.maxLength(maxLength));

        // Add date of birth validation if applicable
        if (fieldName && this.isBirthDateField(fieldName)) {
            validators.push(this.createDateOfBirthValidator(customerType));
        }

        return validators;
    }

    /**
     * Checks if the field is a birth date field
     */
    private isBirthDateField(fieldName: string): boolean {
        return areEqual(fieldName, waiverConstants.BIRTH_DATE);
    }

    /**
     * Creates a date of birth validator based on customer type
     */
    private createDateOfBirthValidator(
        customerType: CustomerType
    ): ValidatorFn {
        const ageOfMajor = this.ageOfMajorCustomer();
        if (!ageOfMajor) return () => null;
        return areEqual(customerType, this.CUSTOMER_TYPE.MINOR)
            ? this._formValidatorService.dobValidatorFactory(0, ageOfMajor)
            : this._formValidatorService.dobValidatorFactory(ageOfMajor);
    }

    /**
     * Retrieves the options for a select-type field, if applicable.
     *
     * @param {CustomerUIMetadataContainerDTOModel} dto - The DTO containing field metadata
     * @param {string} fieldName - The name of the field
     * @returns {DropdownOption[] | undefined} Array of options for the select field, or undefined if not applicable
     */
    private getOptions(
        dto: CustomerUIMetadataContainerDTOModel,
        fieldName: string
    ): DropdownOption[] {
        if (areEqual(fieldName, 'country')) return this.countryOptions();
        if (areEqual(fieldName, 'state')) return [];

        if (areEqual(fieldName, 'gender')) {
            return (
                dto.CustomerFieldValues?.map((opt: string) => ({
                    label: opt,
                    value: opt.split('')[0],
                })) ?? []
            );
        }

        if (
            Array.isArray(dto.CustomerFieldValues) &&
            dto.CustomerFieldValues.length
        ) {
            return dto.CustomerFieldValues.map((opt: string) => ({
                label: opt,
                value: opt,
            }));
        }

        return [];
    }

    /**
     * Retrieves the options for a custom attribute select-type field, if applicable.
     *
     * @param {CustomAttributesContainerDTO} customAttr - The custom attribute DTO
     * @returns {DropdownOption[] | undefined} Array of options for the select field, or undefined if not applicable
     */
    private getCustomAttributeOptions(
        customAttr: CustomAttributesContainerDTO
    ): DropdownOption[] {
        if (
            Array.isArray(
                customAttr.CustomAttributeValueListContainerDTOList
            ) &&
            customAttr.CustomAttributeValueListContainerDTOList.length
        ) {
            return customAttr.CustomAttributeValueListContainerDTOList.map(
                (opt: CustomAttributeValueListContainerDTO) => ({
                    label: opt.Value,
                    value: opt.Value,
                })
            );
        }
        return [];
    }

    generateErrorMessages(fields: DynamicFormField[]): ErrorMessage<Message> {
        const messages: ErrorMessage<Message> = {};

        fields.forEach((field) => {
            const fieldErrors: Message = {};

            if (areEqual(field.fieldName, 'email')) {
                fieldErrors['email'] = 'Please enter a valid email address';
            }
            if (field.required) {
                fieldErrors['required'] = `${field.label} is required`;
            }
            if (field.validators?.pattern) {
                fieldErrors['pattern'] = 'Invalid format';
            }
            if (field.validators?.minLength) {
                fieldErrors[
                    'minlength'
                ] = `Minimum length is ${field.validators.minLength}`;
            }
            if (field.validators?.maxLength) {
                fieldErrors[
                    'maxlength'
                ] = `Maximum length is ${field.validators.maxLength}`;
            }
            if (areEqual(field.fieldName, waiverConstants.BIRTH_DATE)) {
                fieldErrors['minAge'] = 'Age is below the minor age limit.';
                fieldErrors['maxAge'] = 'Age is above the minor age limit.';
            }

            messages[field.fieldName] = fieldErrors;
        });

        return messages;
    }

    createFormGroupFromFields(
        fields: DynamicFormField[],
        fb: FormBuilder
    ): FormGroup {
        const group: Record<string, FormControl<any>> = {};

        fields.forEach((field) => {
            const validators: ValidatorFn[] = [];

            if (field.required) validators.push(Validators.required);
            if (field.validators?.validatorFn) {
                validators.push(
                    ...(Array.isArray(field.validators.validatorFn)
                        ? field.validators.validatorFn
                        : [field.validators.validatorFn])
                );
            }

            group[field.fieldName] = fb.control('', validators);
        });

        return fb.group(group);
    }

    registerCustomer(form: FormGroup): void {
        try {
            const customerPayload =
                this._createCustomerPayloadBuilderService.buildCustomerPayloadFromFormData(
                    form.value,
                    this.customerUIMetadata()
                );
            this._createCustomerServiceDL.customerPayload = customerPayload;
            this._handleRequest(
                this._createCustomerServiceDL.load(),
                () => {
                    const email = form.get('EMAIL')?.value;
                    const password = form.get('password')?.value;
                    this._libAuthLoginBL.login(
                        email,
                        password
                    );
                }
            );
        } catch (error) {
            console.error('Error registering customer:', error);
        }
    }
}
