/**
 * @fileoverview
 * This service is responsible for transforming raw customer form data into properly structured
 * DTO objects that can be sent to the backend API. It handles both major customers and their
 * associated minors, building complex nested objects with proper metadata mapping.
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
 */
import { inject, Injectable } from '@angular/core';
import { CustomerUIMetadataContainerDTOModel, SiteContextService } from 'lib-app-core';
import { waiverConstants } from '../../constants/waiver.constant';

@Injectable()
export class CreateCustomerPayloadBuilderService {
  private readonly _siteContextService = inject(SiteContextService);
  private readonly _siteId = this._siteContextService.siteId;

  /**
   * Builds a complete customer payload from raw form data and metadata
   * 
   * @param rawFormData - The raw customer form data from the UI
   * @param fieldMetadataList - List of field metadata defining how to map form fields to DTO properties
   * @returns Array containing the main customer DTO with optional minor relationships
   * 
   * @throws {Error} When rawFormData is null/undefined
   * @throws {Error} When fieldMetadataList is not an array
   * 
   */
  buildCustomerPayloadFromFormData(
    rawFormData: any,
    fieldMetadataList: CustomerUIMetadataContainerDTOModel[]
  ) {
    if (!rawFormData) throw new Error('Raw customer form data is required');
    if (!Array.isArray(fieldMetadataList)) throw new Error('Field metadata list must be an array');

    const mainCustomerDTO = this.buildCustomerDTOFromFormData(rawFormData, fieldMetadataList);

    // Handle minor relationships if present
    if (rawFormData.minors?.length) {
      mainCustomerDTO.CustomerRelationshipDTOList = rawFormData.minors.map((minorData: any) => ({
        CustomerRelationshipTypeId: waiverConstants.CUSTOMER_RELATIONSHIP_TYPE_ID, // Parent-Minor relationship
        RelatedCustomerDTO: this.buildCustomerDTOFromFormData(minorData, fieldMetadataList, rawFormData)
      }));
    }

    return [mainCustomerDTO];
  }

  /**
   * Builds a customer DTO object from form data using metadata mapping
   * 
   * @param customerFormData - Raw customer form data
   * @param fieldMetadataList - Metadata defining field mappings
   * @param parentCustomerData - Optional parent customer data for minors
   * @returns Properly structured customer DTO object
   * 
   * @private
   */
  private buildCustomerDTOFromFormData(
    customerFormData: any,
    fieldMetadataList: CustomerUIMetadataContainerDTOModel[],
    parentCustomerData?: any
  ) {
    if (!customerFormData) throw new Error('Customer form data is required');
  
    // Merge parent data with current data for minors
    const mergedFormData = parentCustomerData ? { ...parentCustomerData, ...customerFormData } : customerFormData;
    const normalizedFormData = this.normalizeFormDataKeys(mergedFormData);
  
    // Build contact information
    const contactDTOList = this.buildContactInformationList(normalizedFormData);
  
    // Build base customer DTO from metadata mappings
    const customerDTO = this.buildCustomerDTOFromMetadata(normalizedFormData, fieldMetadataList);
  
    // Set required system fields
    customerDTO.SiteId = this._siteId;
  
    // Set contact information
    customerDTO.ContactDTOList = contactDTOList;

    if(customerFormData.password){
      customerDTO.Password = customerFormData.password;
      customerDTO.ProfileDTO.Password = customerFormData.password;
    }
    if(customerFormData.confirmPassword){
      customerDTO.ConfirmPassword = customerFormData.confirmPassword;
    }
  
    // Build and set profile information
    customerDTO.ProfileDTO ??= {};
    customerDTO.ProfileDTO = {
      ...customerDTO.ProfileDTO,
      IsActive: true,
      IsChanged: true,
      IsChangedRecursive: true,
      Id: -1, 
      ContactDTOList: contactDTOList,
    };
  
    // Set additional profile fields for minors
    if (parentCustomerData) {
      customerDTO.ProfileDTO.SiteId = this._siteId;
    }
  
    // Build custom attributes
    customerDTO.CustomDataSetDTO = this.buildCustomAttributesDTO(normalizedFormData, fieldMetadataList);
  
    return customerDTO;
  }

  /**
   * Builds customer DTO object by mapping form fields to DTO properties using metadata
   * 
   * @param normalizedFormData - Form data with normalized keys
   * @param fieldMetadataList - Metadata defining field mappings
   * @returns Customer DTO with mapped properties
   * 
   * @private
   */
  private buildCustomerDTOFromMetadata(
    normalizedFormData: Record<string, any>,
    fieldMetadataList: CustomerUIMetadataContainerDTOModel[]
  ): any {
    const customerDTO: any = {};

    for (const fieldMetadata of fieldMetadataList) {
      const formFieldKey = fieldMetadata.CustomerFieldName?.toLowerCase();
      const dtoPropertyPath = fieldMetadata.EntityFieldName;
      const fieldValue = normalizedFormData[formFieldKey];

      if (!formFieldKey || !dtoPropertyPath) continue;
      if (fieldValue === null || fieldValue === undefined || fieldValue === '') continue;

      this.setNestedPropertyValue(customerDTO, dtoPropertyPath, fieldValue);
    }

    return customerDTO;
  }

  /**
   * Builds custom attributes DTO from form data and metadata
   * 
   * @param normalizedFormData - Form data with normalized keys
   * @param fieldMetadataList - Metadata defining custom attribute mappings
   * @returns CustomDataSetDTO with custom attributes
   * 
   * @private
   */
  private buildCustomAttributesDTO(
    normalizedFormData: Record<string, any>,
    fieldMetadataList: CustomerUIMetadataContainerDTOModel[]
  ) {
    const customDataDTOList = [];

    for (const fieldMetadata of fieldMetadataList) {
      // Check if this is a custom attribute field
      if (fieldMetadata.CustomAttributeFlag === 1 && fieldMetadata.CustomAttributeId > 0) {
        const formFieldKey = fieldMetadata.CustomerFieldName.toLowerCase();
        const fieldValue = normalizedFormData[formFieldKey];

        if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
          customDataDTOList.push({
            CustomDataId: -1,
            CustomDataSetId: -1,
            CustomAttributeId: fieldMetadata.CustomAttributeId,
            CustomDataText: String(fieldValue)
          });
        }
      }
    }

    return {
      CustomDataSetId: -1,
      CustomDataDTOList: customDataDTOList
    };
  }

  /**
   * Sets a value at a nested property path in an object
   * 
   * @param targetObject - The object to set the property in
   * @param propertyPath - Dot-notation path to the property (supports array indices)
   * @param propertyValue - The value to set
   * 
   * @private
   * 
   */
  private setNestedPropertyValue(targetObject: any, propertyPath: string, propertyValue: any): void {
    if (!propertyPath || !targetObject) return;
    
    // Convert array notation [0] to dot notation .0
    const normalizedPath = propertyPath.replace(/\[(\d+)\]/g, '.$1');
    const pathParts = normalizedPath.split('.');
    let currentObject = targetObject;

    // Navigate to the parent of the target property
    for (let i = 0; i < pathParts.length - 1; i++) {
      const currentPart = pathParts[i];
      if (!currentPart) continue;

      const nextPart = pathParts[i + 1];
      const isNextPartArrayIndex = !isNaN(Number(nextPart));

      if (!currentObject[currentPart]) {
        currentObject[currentPart] = isNextPartArrayIndex ? [] : {};
      }

      currentObject = currentObject[currentPart];
    }

    // Set the final property value
    const finalPropertyName = pathParts[pathParts.length - 1];
    if (finalPropertyName) currentObject[finalPropertyName] = propertyValue;
  }

  /**
   * Normalizes form data keys to lowercase for consistent processing
   * 
   * @param formData - Raw form data object
   * @returns Form data with all keys converted to lowercase
   * 
   * @private
   */
  private normalizeFormDataKeys(formData: Record<string, any>): Record<string, any> {
    const normalizedData: Record<string, any> = {};
    for (const key in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        normalizedData[key.toLowerCase()] = formData[key];
      }
    }
    return normalizedData;
  }

  /**
   * Builds contact information DTO list from form data
   * 
   * Maps email and phone fields to the appropriate contact DTO structure
   * expected by the backend API.
   * 
   * @param normalizedFormData - Form data with normalized keys
   * @returns Array of contact DTO objects
   * 
   * @private
   */
  private buildContactInformationList(
    normalizedFormData: Record<string, any>
  ): any[] {
    const contactFieldMappings: {
      formFieldKey: string;
      contactTypeId: number;
      contactType: number;
    }[] = [
      {
        formFieldKey: waiverConstants.EMAIL.toLowerCase(),
        contactTypeId: 2,
        contactType: 1, 
      },
      {
        formFieldKey: waiverConstants.PHONE_DEFAULT.toLowerCase(),
        contactTypeId: 1, 
        contactType: 2, 
      }
    ];
  
    const contactDTOList: any[] = [];
  
    for (const { formFieldKey, contactTypeId, contactType } of contactFieldMappings) {
      const contactValue = normalizedFormData[formFieldKey];
      if (contactValue !== undefined && contactValue !== null && contactValue !== '') {
        contactDTOList.push({
          Attribute1: contactValue,
          ContactTypeId: contactTypeId,
          ContactType: contactType,
        });
      }
    }
    return contactDTOList;
  }
}
