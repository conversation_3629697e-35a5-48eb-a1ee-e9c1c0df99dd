/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-03-12
 */

import { Injectable, inject } from '@angular/core';
import {
    CookieService,
    ExecutionContextBaseInterceptor
} from 'lib-app-core';

@Injectable()
export class ExecutionContextInterceptor extends ExecutionContextBaseInterceptor {
    private readonly cookieService = inject(CookieService);
    protected getWebApiToken(): string | null {
        const webApiToken  = this.cookieService.getCookie('webApiToken');
        return webApiToken;
    }
}
