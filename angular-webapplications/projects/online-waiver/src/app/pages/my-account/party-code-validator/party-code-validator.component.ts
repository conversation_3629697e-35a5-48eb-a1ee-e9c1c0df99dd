/**
 * @fileoverview Party check-in component
 * <AUTHOR>
 */

import { Component, signal, computed, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PartyCheckInBL } from '../../../services/business-layer/party-check-in-bl.service';
import { PartyCheckInDL } from '../../../services/data-layer/party-check-in-dl.service';
import { Router } from '@angular/router';

@Component({
    selector: 'app-party-check-in',
    imports: [CommonModule, FormsModule],
    providers: [PartyCheckInDL, PartyCheckInBL],
    templateUrl: './party-code-validator.component.html',
})
export class PartyCodeValidatorComponent {
    private readonly _partyCheckInBL = inject(PartyCheckInBL);
    private readonly _router = inject(Router);

    otpValue = signal('');

    // use BL signals
    readonly loading = this._partyCheckInBL.loading;
    readonly errorMessage = this._partyCheckInBL.errorMessage;
    readonly reservationData = this._partyCheckInBL.reservationData;

    // computed signals for UI
    showError = computed(
        () => !!this.errorMessage() && !this.reservationData()
    );
    isValidCode = computed(() => !!this.reservationData());

    constructor() {
        // Automatically navigate to check-in page when party code validation succeeds
        effect(() => {
            const reservation = this.reservationData();
            if (reservation) {
                this._router.navigate(['/waivers/check-in'], {
                    queryParams: {
                        code: this.otpValue(),
                        bookingId: reservation.BookingId,
                        trxId: reservation.TrxId,
                    },
                });
            }
        });
    }

    /**
     * Handles OTP input changes and resets validation state
     * @param value - The new OTP value entered by the user
     */
    onOtpChange(value: string): void {
        this.otpValue.set(value);
        this._partyCheckInBL.clearReservationData(); // Reset validation state when user types
    }

    /**
     * Validates the entered party code against the backend
     * Only proceeds if a valid OTP value is present
     */
    validateCode(): void {
        if (!this.otpValue()) return;
        this._partyCheckInBL.validatePartyCode(this.otpValue());
    }
}
