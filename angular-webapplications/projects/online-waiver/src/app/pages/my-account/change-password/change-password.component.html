<div class="bg-surface-white rounded-4xl shadow-lg p-6 w-full flex flex-col md:min-h-[calc(100vh-220px)]">
    <!-- Header -->
    <h1 class="text-lg font-medium text-primary mb-6">
        Change Password
    </h1>

    <!-- Description -->
    <p class="text-sm text-neutral-dark mb-6">
        Your new password must be different from your last three passwords
    </p>

    <!-- Form -->
    <form [formGroup]="changePasswordForm" class="flex-1 flex flex-col">
        <!-- Input Fields Container - Horizontal on medium+ screens, vertical on mobile -->
        <div class="flex flex-col gap-6 mb-6 md:flex-row lg:flex-row">
            <!-- Current Password -->
            <div class="flex flex-col gap-2 flex-1">
                <label class="text-xs font-normal text-primary">
                    Current Password
                </label>
                <div class="relative">
                    <lib-text-input
                        formControlName="currentPassword"
                        id="current-password"
                        type="password"
                        placeholder="Enter current password"
                        [showPasswordToggle]="true"
                        [errorMessages]="errorMessages.currentPassword"
                        customClass="w-full px-4 py-4 border border-surface rounded-xl bg-white/50 text-sm font-medium"
                    ></lib-text-input>
                </div>
            </div>

            <!-- New Password -->
            <div class="flex flex-col gap-2 flex-1">
                <label class="text-xs font-normal text-primary">
                    New Password
                </label>
                <div class="relative">
                    <lib-text-input
                        formControlName="newPassword"
                        id="new-password"
                        type="password"
                        placeholder="Enter new password"
                        [showPasswordToggle]="true"
                        [errorMessages]="errorMessages.newPassword"
                        customClass="w-full px-4 py-4 border border-surface rounded-xl bg-white/50 text-sm font-medium"
                    ></lib-text-input>
                </div>
            </div>

            <!-- Confirm Password -->
            <div class="flex flex-col gap-2 flex-1">
                <label class="text-xs font-normal text-primary">
                    Re-enter New Password
                </label>
                <div class="relative">
                    <lib-text-input
                        formControlName="confirmPassword"
                        id="confirm-password"
                        type="password"
                        placeholder="Re-enter new password"
                        [showPasswordToggle]="true"
                        [errorMessages]="errorMessages.confirmPassword"
                        customClass="w-full px-4 py-4 border border-surface rounded-xl bg-white/50 text-sm font-medium"
                    ></lib-text-input>
                </div>
            </div>
        </div>

        <!-- Footer with Buttons - Fixed at bottom -->
        <div class="border-t border-surface pt-6">
            <div class="flex flex-wrap gap-4">
                <!-- Update Button -->
                <button
                    type="button"
                    (click)="onSubmit()"
                    [disabled]="changePasswordForm.invalid || _changePasswordBL.loading()"
                    class="w-80 h-12 px-6 py-2.5 bg-black-900 text-surface-white font-medium text-base rounded-4xl disabled:bg-surface disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                    <span>{{ _changePasswordBL.loading() ? 'Updating...' : 'Update' }}</span>
                </button>

                <!-- Cancel Button -->
                <button
                    type="button"
                    class="w-80 h-12 px-6 py-2.5 bg-surface-white border border-primary text-primary font-medium text-base rounded-[30px] hover:bg-gray-50 flex items-center justify-center gap-2"
                >
                    Cancel
                </button>
            </div>
        </div>
    </form>

    <!-- Error Message -->
    @if(_changePasswordBL.errorMessage()) {
        <div class="mt-4 p-4 bg-feedback-surface-error border border-feedback-error/20 rounded-xl">
            <p class="text-feedback-error text-sm">{{ _changePasswordBL.errorMessage() }}</p>
        </div>
    }
</div>