/**
 * @fileoverview Change Password Component
 * <AUTHOR>
 * @version 1.0.0
 */

import { Component, effect, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TextInputComponent, FormValidatorService } from 'lib-ui-kit';
import { STRONG_PASSWORD_REGEX } from 'lib-auth';
import { formValidationMessages } from 'lib-app-core';
import { ChangePasswordBL } from '../../../services/business-layer/change-password-bl.service';
import { ChangePasswordDL } from '../../../services/data-layer/change-password-dl.service';
import { ChangePasswordEncryptionService } from 'lib/lib-auth/src/lib/services/change-password-encryption.service';
import { LibUserDetailDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-user-details-dl.service';

interface ChangePasswordFormData {
    currentPassword: FormControl<string | null>;
    newPassword: FormControl<string | null>;
    confirmPassword: FormControl<string | null>;
}

@Component({
    selector: 'app-change-password',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TextInputComponent,
    ],
    providers: [
        FormValidatorService,
        ChangePasswordBL,
        ChangePasswordDL,
        ChangePasswordEncryptionService,
        LibUserDetailDL,
    ],
    templateUrl: './change-password.component.html',
})
export class ChangePasswordComponent {
    protected readonly _changePasswordBL = inject(ChangePasswordBL);
    private readonly _fb = inject(FormBuilder);
    protected readonly _router = inject(Router);
    private readonly _formValidatorService = inject(FormValidatorService);

    readonly changePasswordForm: FormGroup<ChangePasswordFormData>;

    errorMessages = {
        currentPassword: formValidationMessages.password,
        newPassword: formValidationMessages.password,
        confirmPassword: formValidationMessages.confirmPassword,
    };

    fieldDescription = {
        newPassword: formValidationMessages.password.pattern,
    };

    constructor() {
        this.changePasswordForm = this._fb.group({
            currentPassword: ['', [Validators.required]],
            newPassword: [
                '',
                [
                    Validators.required,
                    Validators.pattern(STRONG_PASSWORD_REGEX),
                ],
            ],
            confirmPassword: ['', [Validators.required]],
        }, {
            validators: [
                this._formValidatorService.passwordMatchValidator('newPassword', 'confirmPassword')
            ],
        });

        // Reset error state on form changes
        this.changePasswordForm.valueChanges.subscribe(() => {
            if (this._changePasswordBL.errorMessage()) {
                this._changePasswordBL.errorMessage.set(null);
            }
        });
    }

    onSubmit() {
        if (this.changePasswordForm.valid) {
            const { currentPassword, newPassword } = this.changePasswordForm.value;
            
            this._changePasswordBL.changePassword(
                {
                    currentPassword: currentPassword!,
                    newPassword: newPassword!,
                },
                () => {
                    // Success callback - navigate back to profile
                    this._router.navigate(['/my-accounts/my-profile']);
                }
            );
        }
    }
}
