<lib-registration-form-base
    [fields]="dynamicMajorFields()"
    [fieldDescription]="fieldDescription"
    [errorMessages]="errorMessages"
    [registerForm]="registerForm"
    [isFieldsLoading]="customerUIMetadataServiceBL.isUIDataLoading()"
    [isSubmitting]="customerUIMetadataServiceBL.isSubmitting()"
    (submit)="onSubmit($event)"
    (termsAccepted)="onTermsAccepted($event)"
>
    <div add-minor-form>
        @for (minorForm of minorForms.controls; track $index) {
        <app-add-minor-form
            [fields]="dynamicMinorFields()"
            [minorForm]="minorForm"
            [errorMessages]="errorMessages"
            [index]="$index"
            (remove)="removeMinorForm($index)"
        ></app-add-minor-form>
        }

        <!-- Add Minor Button -->
        <button
            type="button"
            (click)="addMinorForm()"
            class="w-full md:w-[30%] py-2 px-4 mb-6 bg-surface-white border border-primary rounded-full text-primary font-medium flex items-center justify-center gap-2"
        >
            <img src="assets/icons/person-plus.svg" alt="Image" />
            Add Minor
        </button>
    </div>
</lib-registration-form-base>

@if(customerUIMetadataServiceBL.errorMessage()) {
    <p class="text-red-500 mt-4">
        {{ customerUIMetadataServiceBL.errorMessage() }}
    </p>
}
