import { CommonModule } from '@angular/common';
import {
    Component,
    DestroyRef,
    effect,
    inject,
    Injector,
    OnInit,
    runInInjectionContext,
    signal
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
    AbstractControl,
    FormArray,
    FormBuilder,
    FormControl,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import {
    areEqual,
    CountryContainerServiceDL,
    CreateCustomerServiceDL,
    CustomerUIMetadataServiceDL,
    formValidationMessages,
    RichContentsServiceDL,
} from 'lib-app-core';
import { LibAuthLoginBL, LibAuthLoginDL, RegistrationFormBaseComponent, STRONG_PASSWORD_REGEX } from 'lib-auth';
import {
    DynamicFormField,
    ErrorMessage,
    FieldDescription,
    FormValidatorService
} from 'lib-ui-kit';
import { waiverConstants } from '../../constants/waiver.constant';
import { CreateCustomerPayloadBuilderService } from '../../services/buisiness-layer/create-customer-payload-builder.service';
import {
    CustomerUIMetadataServiceBL,
    Message,
} from '../../services/buisiness-layer/customer-ui-metadata-bl.service';
import { AddMinorFormComponent } from '../add-minor-form/add-minor-form.component';
import { LoginEncryptionService } from 'lib/lib-auth/src/lib/services/login-encryption.service';

interface RegisterFormData {
    password: FormControl<string | null>;
    confirmPassword: FormControl<string | null>;
    minors: FormArray<FormGroup>;
    [key: string]: AbstractControl<any>;
}

@Component({
    selector: 'app-registration-form-waiver',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        AddMinorFormComponent,
        RegistrationFormBaseComponent,
    ],
    providers: [
        CustomerUIMetadataServiceDL,
        CreateCustomerServiceDL,
        CountryContainerServiceDL,
        RichContentsServiceDL,
        CustomerUIMetadataServiceBL,
        CreateCustomerPayloadBuilderService,
        FormValidatorService,
        LibAuthLoginDL,
        LibAuthLoginBL,
        LoginEncryptionService
    ],
    templateUrl: './registration-form-waiver.component.html',
    styleUrl: './registration-form-waiver.component.css',
})
export class RegistrationFormWaiverComponent implements OnInit {
    readonly dateFormat = waiverConstants.DATE_FORMAT;
    private readonly _fb = inject(FormBuilder);
    private readonly _injector = inject(Injector);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _formValidatorService = inject(FormValidatorService);
    protected readonly customerUIMetadataServiceBL = inject(CustomerUIMetadataServiceBL);

    protected readonly dynamicMajorFields = signal<DynamicFormField[]>([]);
    protected readonly dynamicMinorFields = signal<DynamicFormField[]>([]); 

    errorMessages: ErrorMessage<Message> = {};
    fieldDescription: FieldDescription<Message> = {
        password: formValidationMessages.password.pattern,
    };

    registerForm = new FormGroup<RegisterFormData>(
        {
            password: new FormControl('', [
                Validators.required,
                Validators.pattern(STRONG_PASSWORD_REGEX),
            ]),
            confirmPassword: new FormControl('', [Validators.required]),
            minors: new FormArray<FormGroup>([]),
        },
        {
            validators: [this._formValidatorService.passwordMatchValidator('password', 'confirmPassword')],
        }
    );

    /**
     * Initializes the component
     */
    ngOnInit(): void {
        this.setupFields();
    }

    /**
     * Sets the terms and conditions value
     * @param value - The value to set
     */
    onTermsAccepted(value: boolean): void {
       this.registerForm.get(waiverConstants.TERMS_AND_CONDITIONS)?.setValue(value);
    }

    /**
     * Gets the minor forms
     * @returns The minor forms
     */
    get minorForms(): FormArray<FormGroup> {
        return this.registerForm.get('minors') as FormArray<FormGroup>;
    }

    /**
     * Adds a minor  to the form
     */
    addMinorForm(): void {
        const group =
            this.customerUIMetadataServiceBL.createFormGroupFromFields(
                this.dynamicMinorFields(),
                this._fb
            );
        this.minorForms.push(group);
    }

    /**
     * Removes a minor from the form
     * @param index - The index of the minor to remove
     */
    removeMinorForm(index: number): void {
        this.minorForms.removeAt(index);
    }

    /**
     * Sets up the country and state dependency
     */
    private setupCountryStateDependency(): void {
        const countryControl = this.registerForm.get(waiverConstants.COUNTRY);
        const stateControl = this.registerForm.get(waiverConstants.STATE);
        const stateField = this.dynamicMajorFields().find(
            (f) => areEqual(f.fieldName, waiverConstants.STATE)
        );

        if (countryControl && stateControl && stateField) {
            countryControl.valueChanges
                .pipe(takeUntilDestroyed(this._destroyRef))
                .subscribe((countryId) => {
                    const country = this.customerUIMetadataServiceBL
                        .countryContainer()
                        .find((c) => areEqual(c.CountryId, Number(countryId)));
                    stateField.options = country
                        ? country.StateContainerDTOList.map((s) => ({
                              label: s.Description,
                              value: String(s.StateId),
                          }))
                        : [];
                    stateControl.reset();
                });
        }
    }

    /**
     * Sets up the fields for the form
     */
    private setupFields(): void {
        runInInjectionContext(this._injector, () => {
            this.customerUIMetadataServiceBL.loadInitialData();
            effect(() => {
                if(this.customerUIMetadataServiceBL.isUIDataLoading()) {
                    return;
                }
                const majorFields =
                    this.customerUIMetadataServiceBL.majorFormFields();
                const minorFields =
                    this.customerUIMetadataServiceBL.minorFormFields();

                this.dynamicMajorFields.set(majorFields);
                this.dynamicMinorFields.set(minorFields);

                // Add dynamic major fields to form
                majorFields.forEach((field) => {
                    if (!this.registerForm.contains(field.fieldName)) {
                        this.registerForm.addControl(
                            field.fieldName,
                            this.customerUIMetadataServiceBL
                                .createFormGroupFromFields([field], this._fb)
                                .get(field.fieldName)!
                        );
                    }
                });

                // Generate error messages once fields are ready
                this.errorMessages = {
                    ...formValidationMessages,
                    ...this.customerUIMetadataServiceBL.generateErrorMessages(majorFields),
                };
                this.registerForm.get(waiverConstants.OPT_IN_PROMOTIONS_MODE)?.disable();
                this.setupCountryStateDependency();
                this.setupOptInPromotions();
            });
        });
    }

    /**
     * Sets up the opt-in promotions functionality
     */
    private setupOptInPromotions(): void {
        const optInPromotionsControl = this.registerForm.get(waiverConstants.OPT_IN_PROMOTIONS);
        const optInPromotionsModeControl = this.registerForm.get(waiverConstants.OPT_IN_PROMOTIONS_MODE);
        optInPromotionsControl?.valueChanges.subscribe((value) => {
            if (value) {
                optInPromotionsModeControl?.enable();
                optInPromotionsModeControl?.setValidators([Validators.required]);
            } else {
                optInPromotionsModeControl?.reset();
                optInPromotionsModeControl?.disable();
                optInPromotionsModeControl?.setValidators([]);
            }
        });
    }

    /**
     * Submits the form
     * @param form - The form to submit
     */
    onSubmit(form: FormGroup): void {
        this.customerUIMetadataServiceBL.registerCustomer(form);
    }
}
