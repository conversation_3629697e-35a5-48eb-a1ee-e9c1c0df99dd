/**
 * @fileoverview Terms and conditions component
 * <AUTHOR>
 * @version 1.0.0
 * @createdAt 2025-08-05
*/

import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    input,
    OnDestroy,
    output,
    PLATFORM_ID
} from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    ReactiveFormsModule,
} from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { CustomerUIMetadataServiceDL } from 'lib-app-core';
import { CheckBoxComponent, ModalComponent, SkeletonLoaderComponent } from 'lib-ui-kit';
import { waiverConstants } from '../../constants/waiver.constant';
import { Base64PdfBLService } from '../../services/buisiness-layer/base64-pdf-bl.service';
import { CustomerUIMetadataServiceBL } from '../../services/buisiness-layer/customer-ui-metadata-bl.service';
import { TermsAndConditionsBL } from '../../services/buisiness-layer/terms-and-condtion-bl.service';

@Component({
    selector: 'app-terms-and-conditions',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ModalComponent,
        CheckBoxComponent,
        SkeletonLoaderComponent,
    ],
    providers: [
        CustomerUIMetadataServiceDL,
        Base64PdfBLService,
        TermsAndConditionsBL,
    ],
    templateUrl: './terms-and-conditions.component.html',
    styleUrl: './terms-and-conditions.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: { ngSkipHydration: 'true' },
})
export class TermsAndConditionsComponent implements OnDestroy {
    // Inputs
    enabled = input<boolean>(false);
    termsAccepted = output<boolean>();

    readonly termsAndConditions: FormGroup;

    // Services
    protected readonly customerUIMetadataServiceBL = inject(
        CustomerUIMetadataServiceBL
    );
    protected readonly termsAndConditionsBL = inject(TermsAndConditionsBL);
    private readonly sanitizer = inject(DomSanitizer);
    private readonly platformId = inject(PLATFORM_ID);

    // Expose signals from the business layer service
    readonly showModal = this.termsAndConditionsBL.showModal;
    readonly modalContent = this.termsAndConditionsBL.modalContent;
    readonly modalTitle = this.termsAndConditionsBL.modalTitle;
    readonly pdfBlobUrl = this.termsAndConditionsBL.pdfBlobUrl;

    readonly sanitizedPdfUrl = computed<SafeResourceUrl | null>(() => {
        const rawBlobUrl = this.pdfBlobUrl();

        if (rawBlobUrl) {
            return this.sanitizer.bypassSecurityTrustResourceUrl(rawBlobUrl);
            // return this.sanitizer.sanitize(SecurityContext.RESOURCE_URL, rawBlobUrl);
        }
        return null;
    });

    readonly showTermsAndConditions = computed(() => {
        return (
            this.enabled() &&
            !!(
                this.termsAndConditionsBL.termsAndConditions() ||
                this.termsAndConditionsField()
            ) &&
            !this.termsAndConditionsBL.loading()
        );
    });

    constructor(private fb: FormBuilder) {
        this.termsAndConditions = this.fb.group({
            termsAndConditions: new FormControl<boolean>(false, {
                nonNullable: true,
            }),
        });

        this.termsAndConditions.valueChanges.subscribe((value) => {
            this.termsAccepted.emit(value.termsAndConditions);
        });

        this.termsAndConditionsBL.loadRichContents();
    }

    readonly termsAndConditionsField = computed(() => {
        const metadata = this.customerUIMetadataServiceBL.customerUIMetadata();
        return (
            metadata.find(
                (field) =>
                    field.CustomerFieldName ===
                    waiverConstants.TERMS_AND_CONDITIONS
            ) || null
        );
    });

    readonly termsAndConditionsHTML = computed(() => {
        const termsField = this.termsAndConditionsField();
        if (!termsField?.EntityFieldCaption) {
            return '';
        }

        // Process the HTML to add classes to anchor tags
        return this.addClassesToAnchorTags(termsField.EntityFieldCaption);
    });

    /**
     * Adds underline and text-secondary-blue classes to anchor tags in the HTML string
     *
     * @param html - The HTML string to process
     * @returns The processed HTML string with anchor tags having the specified classes
     */
    private addClassesToAnchorTags(html: string): string {
        // Create a temporary div to parse the HTML
        if (isPlatformBrowser(this.platformId)) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // Find all anchor tags and add the classes
            const anchorTags = tempDiv.querySelectorAll('a');
            anchorTags.forEach((anchor: HTMLAnchorElement) => {
                anchor.classList.add('underline', 'text-secondary-blue');
            });

            return tempDiv.innerHTML;
        }
        return html;
    }

    /**
     * Closes the modal
     */
    onModalClose(): void {
        this.termsAndConditionsBL.closeModal();
    }

    /**
     * Prevents the default behavior of the event and opens the terms and conditions modal
     *
     * @param event - The event to prevent
     */
    onTermsLinkClick(event: Event): void {
        event.preventDefault();
        event.stopPropagation();
        this.onTermsClick();
    }

    /**
     * Opens the terms and conditions modal
     */
    onTermsClick(): void {
        this.termsAndConditionsBL.openTermsAndConditions();
    }

    /**
     * Closes the modal when the component is destroyed
     */
    ngOnDestroy(): void {
        this.termsAndConditionsBL.closeModal();
    }
}
