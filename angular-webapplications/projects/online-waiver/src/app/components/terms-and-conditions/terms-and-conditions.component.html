@if (showTermsAndConditions()) {
<div class="flex gap-2 items-center text-sm">
    <form [formGroup]="termsAndConditions">
        <lib-checkbox
            formControlName="termsAndConditions"
            [id]="'terms-checkbox'"
            customClass="w-[18px] h-[18px]"
            class="self-end"
        ></lib-checkbox>
    </form>
    <label for="terms-checkbox">
        @if (termsAndConditionsBL.termsAndConditions()) {
        <!-- RichContents has high priority - show button that opens modal -->
        <span>
            I agree to the
            <button
                type="button"
                (click)="onTermsClick()"
                class="underline text-secondary-blue"
            >
                {{
                    termsAndConditionsBL.termsAndConditions()?.ContentName ||
                        "Terms and Conditions"
                }}
            </button>
        </span>
        } @else if (termsAndConditionsField()?.EntityFieldCaption) {
        <!-- CustomerUIMetadata - display with working links using [innerHTML] -->
        <span [innerHTML]="termsAndConditionsHTML()"></span>
        }
    </label>
</div>
} @else{
<lib-skeleton-loader
    skeletonClass="animate-pulse h-5 w-80 bg-surface-lightest rounded-md"
/>
}

<!-- Modal for displaying terms and conditions -->
<lib-modal
    [isOpen]="showModal()"
    [dialogueHeader]="modalTitle()"
    (closeModal)="onModalClose()"
    [modalContent]="pdfViewerContent"
>
</lib-modal>

<!-- PDF Viewer Modal content -->
<ng-template #pdfViewerContent>
    <div class="px-5 pb-5">
        @if (sanitizedPdfUrl()) {
        <iframe
            [src]="sanitizedPdfUrl()"
            title="PDF Viewer"
            loading="lazy"
            class="min-h-[70vh] md:min-h-[80vh] min-w-[50vw] object-contain flex justify-center"
        >
        </iframe>
        }@else{
        <div
            class="text-red-500 text-center min-w-[20vw] min-h-[20vh] flex items-center justify-center"
        >
            No PDF found, please contact support.
        </div>
        }
    </div>
</ng-template>
