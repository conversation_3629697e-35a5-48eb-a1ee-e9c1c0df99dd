/**
 * @fileoverview Add Minor Form Component
 *
 * This component is responsible for rendering the "Add Minor" form,
 * applying the appropriate theme configuration, and optionally inheriting
 * a theme from the parent component.
 *
 * <AUTHOR>
 * @version 1.0.1
 * @created 2024-09-18
 */

import { CommonModule } from '@angular/common';
import { Component, Inject, input, Optional, output } from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    ReactiveFormsModule,
} from '@angular/forms';
import { AddMinorFormTheme } from '@theme/waiver/waiver.theme';
import { getTemplateUrl, PARENT_FUNCTION_TOKEN } from 'lib-app-core';
import {
    DynamicFormField,
    ErrorMessage
} from 'lib-ui-kit';
import { SiteBaseComponent } from '../../core/sitebase.component';
import { DynamicFormComponent } from "../dynamic-form/dynamic-form.component";

interface MinorFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
}

@Component({
    standalone: true,
    selector: 'app-add-minor-form',
    imports: [
    CommonModule,
    ReactiveFormsModule,
    DynamicFormComponent
],
    templateUrl: getTemplateUrl(AddMinorFormTheme, 'add-minor-form'),
    styleUrls: ['./add-minor-form.component.scss'], // Ensuring correct array format
})
export class AddMinorFormComponent extends SiteBaseComponent {
    minorForm = input.required<FormGroup>();
    index = input<number>(0);
    remove = output<number>();
    fields = input.required<DynamicFormField[]>();
    errorMessages = input<ErrorMessage<MinorFormData>>();

    /**
     * Stores the inherited theme from the parent component, if available.
     */
    parentTheme: Record<string, any> = {};

    constructor(
        /**
         * Injects an optional function that provides the parent component's theme.
         * If the parent does not provide this function, the component falls back to its own theme.
         */
        @Optional() @Inject(PARENT_FUNCTION_TOKEN) private getPageTheme: (() => Record<string, any>) | null,
        private fb: FormBuilder
    ) {
        super();

        // If a parent theme function is provided, retrieve the theme from the parent
        this.parentTheme = this.getPageTheme?.() ?? {};
    }

    /**
     * Retrieves the relevant theme for this component.
     * - Merges the parent theme (if available) with the local `AddMinorFormTheme`.
     * - Ensures a fallback to an empty object if `getPageTheme` is not provided.
     *
     * @returns A merged theme object with parent theme properties and local theme properties.
     */
    protected getRelevantTheme(): Record<string, any> {
        return { ...this.parentTheme, ...AddMinorFormTheme };
    }

    removeMinor(): void {
        this.remove.emit(this.index());
    }
}
